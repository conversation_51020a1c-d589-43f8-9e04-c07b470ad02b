/* eslint-disable react-native/no-inline-styles */
import React, {forwardRef, useEffect, useRef, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ScrollView,
  Image,
  Pressable,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  FlatList,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {TypoSkin} from '../../../assets/skin/typography';
import {
  AppSvg,
  Checkbox,
  ComponentStatus,
  FBottomSheet,
  hideBottomSheet,
  showBottomSheet,
  showSnackbar,
} from 'wini-mobile-components';
import iconSvg from '../../../svg/icon';
import {ColorThemes} from '../../../assets/skin/colors';
import ModalPromotion from './ModalPromotion';
import WScreenFooter from '../../../Screen/Layout/footer';
import {AppDispatch, RootState} from '../../../redux/store/store';
import {useDispatch, useSelector} from 'react-redux';
import ScreenHeader from '../../../Screen/Layout/header';
import {TextInput} from 'react-native-paper';
import {fetchCategories} from '../../../redux/actions/categoryAction';
import CartPromotion from './Cart/CartPromotion';
import {ProductActions} from '../../../redux/reducers/ShoptReducer';
import {useSelectorShopState} from '../../../redux/hook/shopHook ';
import {handleActionsProduct} from '../../../redux/reducers/ProductReducer';
import {useSelectorProductState} from '../../../redux/hook/productHook ';
import ConfigAPI from '../../../Config/ConfigAPI';
import {DataController} from '../../../base/baseController';
import ProductDA from '../../../modules/Product/da';
import EmptyPage from '../../../Screen/emptyPage';

const ShopPromortionComponent = () => {
  const popupRef = useRef<any>(null);
  const [activeButton, setActiveButton] = useState<string>('Tất cả');
  const [productPromotion, setProductPromotion] = useState<any[]>([]);
  const [isShow, setIsShow] = useState<boolean>(false);
  const [getSelectProduct, setGetSelectProduct] = useState<string>('');
  const [discountValue, setDiscountValue] = useState<string>('');
  const [dataDiscount, setDataDiscount] = useState<any[]>([]);
  const [allCategoryIds, setAllCategoryIds] = useState<any[]>([]);
  const dispatch = useDispatch<any>();
  const productDA = new DataController('Product');
  const shopInfo = useSelectorShopState().data;
  const {data: dataCategory} = useSelector(
    (state: RootState) => state.category,
  );
  const handleShow = () => {
    setIsShow(true);
    setDataDiscount(productPromotion);
  };

  const closeModal = () => {
    setIsShow(false);
  };

  const getAllCategoryIds = (dataCategory: any) => {
    let ids: any[] = [];
    dataCategory.forEach((cat: any) => {
      if (cat.Children && cat.Children.length > 0) {
        // Nếu có Children, lấy Id của các Children
        ids = ids.concat(
          cat.Children.map((child: any) => {
            return {
              Id: child.Id,
              Name: child.Name,
            };
          }),
        );
      } else {
        // Nếu không có Children, lấy Id của chính nó
        ids.push({
          Id: cat.Id,
          Name: cat.Name,
        });
      }
    });
    console.log('ids', ids);
    setAllCategoryIds(ids);
  };

  const handleEditPromotion = (item: any) => {
    setGetSelectProduct(item?.Id);
  };

  const handleCancelEditPromotion = () => {
    setGetSelectProduct('');
  };

  const getInforProductPromotion = async (CategoryId?: string) => {
    console.log('check-CategoryId', CategoryId);
    let response;
    if (CategoryId) {
      response = await productDA.getListSimple({
        query: `@ShopId:{${shopInfo[0]?.Id}} @CategoryId:{${CategoryId}}`,
      });
    } else {
      response = await productDA.getListSimple({
        query: `@ShopId:{${shopInfo[0]?.Id}}`,
      });
    }

    console.log('check-response-107', response?.data?.length);
    if (response?.code === 200) {
      if (response?.data && response?.data?.length > 0) {
        let filterData = response?.data?.filter((item: any) => item?.Discount);
        setProductPromotion(filterData);
      } else {
        setProductPromotion([]);
      }
    }
  };

  const editPromotion = async (item: any) => {
    let respone = await productDA.edit([
      {...item, Discount: Number(discountValue)},
    ]);
    console.log('check-respone', respone);
    if (respone?.code === 200) {
      getInforProductPromotion();
      setGetSelectProduct('');
    }
  };

  const deletePromotion = async (item: any) => {
    let respone = await productDA.edit([{...item, Discount: 0}]);
    console.log('check-respone', respone);
    if (respone?.code === 200) {
      getInforProductPromotion();
      setGetSelectProduct('');
    }
  };

  useEffect(() => {
    dispatch(fetchCategories());
    getAllCategoryIds(dataCategory);
  }, []);

  useEffect(() => {
    getInforProductPromotion();
  }, [isShow]);

  const handleSelectAllData = (ref: any) => {
    hideBottomSheet(ref as any);
  };

  const handleSelectMenu = async (type: string, item?: any) => {
    console.log('check-item', item);
    setActiveButton(type);
    if (!item) {
      await getInforProductPromotion();
    } else {
      await getInforProductPromotion(item?.Id);
    }
  };

  const handleDeleteAllPromotion = async () => {
    console.log('check-productPromotion', productPromotion);
    let arrayData: any[] = [];
    for (let Pro of productPromotion) {
      arrayData.push({...Pro, Discount: 0});
    }
    let respone = await productDA.edit(arrayData);
    if (respone?.code === 200) {
      getInforProductPromotion();
    }
  };

  return (
    <View style={styles.content}>
      <FBottomSheet ref={popupRef} />
      <View style={styles.PromotionMenu}>
        <Text style={styles.label}>Danh sách</Text>
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => handleDeleteAllPromotion()}>
          <AppSvg SvgSrc={iconSvg.delete} size={12} />
          <Text style={styles.buttonText}>Xóa hàng loạt</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.updateButton}
          onPress={() => handleShow()}>
          <AppSvg SvgSrc={iconSvg.updateAll} size={12} />
          <Text style={styles.buttonTextSuccess}>Cập nhật hàng loạt</Text>
        </TouchableOpacity>
      </View>
      <View style={{flexDirection: 'row', alignItems: 'center'}}>
        <TouchableOpacity
          style={
            activeButton === 'Tất cả' ? styles.activeButton : styles.button
          }
          onPress={() => handleSelectMenu('Tất cả')}>
          <Text style={styles.buttonTextMenu}>
            Tất cả ({productPromotion?.length})
          </Text>
        </TouchableOpacity>
        <FlatList
          data={allCategoryIds}
          style={{flex: 1}}
          keyExtractor={(item, i) => `${i} ${item.Id}`}
          horizontal={true}
          renderItem={({item, index}) => (
            <View style={styles.PromorionNabar} key={`item-${index}`}>
              <TouchableOpacity
                style={
                  activeButton === item?.Name
                    ? styles.activeButton
                    : styles.button
                }
                onPress={() => handleSelectMenu(item?.Name, item)}>
                <Text style={styles.buttonTextMenu}>{item?.Name}</Text>
              </TouchableOpacity>
            </View>
          )}
        />
      </View>
      <View style={styles.menuDetail}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerText}></Text>
          <Text style={styles.headerText}>Ảnh</Text>
          <Text style={styles.headerText}>
            Sản phẩm <AppSvg SvgSrc={iconSvg.dropDown} size={16} />
          </Text>
          <Text style={styles.headerText}>
            KM <AppSvg SvgSrc={iconSvg.dropDown} size={16} />
          </Text>
          <Text style={styles.headerText}>
            TT <AppSvg SvgSrc={iconSvg.dropDown} size={16} />
          </Text>
        </View>
        {productPromotion && productPromotion?.length > 0 ? (
          <FlatList
            data={productPromotion}
            style={{flex: 1}}
            keyExtractor={(item, i) => `${i} ${item.Id}`}
            renderItem={({item, index}) => (
              <View style={styles.row} key={`item-${index}`}>
                <View style={{}}>
                  <Checkbox value={false} onChange={() => {}} size={24} />
                </View>
                <View style={styles.imageContainer}>
                  <Image
                    source={{
                      uri: ConfigAPI.urlImg + item?.Img,
                    }} // Thay bằng URL ảnh thực tế
                    style={styles.image}
                  />
                </View>
                <Text style={styles.text}>{item?.Name}</Text>
                {getSelectProduct !== item?.Id ? (
                  <Text style={styles.textTwo}>
                    {item?.Discount ? item?.Discount : 0}%
                  </Text>
                ) : (
                  <TextInput
                    style={{
                      width: 50,
                      marginRight: 20,
                      backgroundColor: '#f0f0f0',
                      color: 'black',
                    }}
                    value={discountValue}
                    defaultValue={item?.Discount}
                    onChange={e => setDiscountValue(e.nativeEvent.text)}
                  />
                )}

                {getSelectProduct !== item?.Id ? (
                  <View style={styles.actions}>
                    <TouchableOpacity onPress={() => handleEditPromotion(item)}>
                      <AppSvg SvgSrc={iconSvg.editPromotion} size={24} />
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => deletePromotion(item)}>
                      <AppSvg SvgSrc={iconSvg.deletePromotion} size={24} />
                    </TouchableOpacity>
                  </View>
                ) : (
                  <View style={styles.actions}>
                    <TouchableOpacity onPress={() => editPromotion(item)}>
                      <AppSvg SvgSrc={iconSvg.confirm} size={24} />
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() => handleCancelEditPromotion()}>
                      <AppSvg SvgSrc={iconSvg.cancelEdit} size={24} />
                    </TouchableOpacity>
                  </View>
                )}
              </View>
            )}
          />
        ) : (
          <View
            style={{
              flex: 1,
              alignItems: 'center',
              justifyContent: 'center',
              paddingBottom: 100,
            }}>
            <EmptyPage />
          </View>
        )}
      </View>
      <WScreenFooter style={{width: '100%', paddingHorizontal: 20}}>
        <TouchableOpacity
          style={styles.buyButton}
          onPress={() => {
            showBottomSheet({
              ref: popupRef,
              enableDismiss: true,
              children: (
                <BottomSheetPromotion
                  ref={popupRef}
                  handleShow={() => handleShow()}
                  handleGetDataDiscount={value => setDataDiscount(value)}
                  handleSelectAllData={ref => hideBottomSheet(ref as any)}
                />
              ),
            });
          }}>
          <Text style={styles.actionButtonText}>Thêm sản phẩm khuyến mại</Text>
        </TouchableOpacity>
      </WScreenFooter>
      <View style={{zIndex: 1000, position: 'absolute', top: 0, left: 0}}>
        <ModalPromotion
          isShow={isShow}
          closeModal={closeModal}
          svgSrc={iconSvg.updateAll}
          title="Cập nhật khuyến mãi hàng loạt"
          dataDiscount={dataDiscount}
          ref={popupRef}
          handleSelectAllData={handleSelectAllData}
        />
      </View>
    </View>
  );
};

const BottomSheetPromotion = forwardRef<
  typeof FBottomSheet,
  {
    handleShow: (data?: any) => void;
    handleGetDataDiscount: (data: any[]) => void;
    handleSelectAllData: (ref: any) => void;
  }
>((props, ref) => {
  const {data} = useSelector((state: RootState) => state.category);
  const [dataCategory, setDataCategory] = useState<any[]>([]);
  const [selectTwo, setSelectTwo] = useState<boolean>(false);
  const [selectThree, setselectThree] = useState<boolean>(false);
  const [dataDiscount, setDataDiscount] = useState<any[]>([]);
  const navigation = useNavigation();
  const productDA = new DataController('Product');
  const shopInfo = useSelectorShopState().data;

  useEffect(() => {
    if (data && data?.length > 0) {
      console.log('check-data', data);
      setDataCategory(data);
    }
  }, [data]);

  // hàm chọn danh mục dữ liệu ở mục 1
  const handleSelectCategory = async (item: any) => {
    console.log('check-SelectTwo', selectTwo);
    if (item && item?.Children && item?.Children?.length > 0) {
      setDataCategory(item?.Children);
    } else {
      await getProductByCategory(item?.Id);
    }
  };
  const handleSelectAllCategory = (item: any) => {
    setSelectTwo(!selectTwo);
  };
  const handleSelectDiscount = () => {
    props.handleShow();
  };

  const getProductByCategory = async (CategoryId: any) => {
    setSelectTwo(!selectTwo);
    console.log('check-CategoryId', CategoryId);
    let respone = await productDA.getListSimple({
      query: `@ShopId:{${shopInfo[0]?.Id}} @CategoryId: {${CategoryId}}`,
    });
    console.log('check-respone', respone);
    if (respone?.code === 200) {
      let filterData = respone?.data?.filter((item: any) => !item?.Discount);
      setDataCategory(filterData);
      props.handleGetDataDiscount(filterData);
      setselectThree(true);
    }
  };

  const getdataByMenuTwo = async (item: any) => {
    if (item && item?.Id) {
      await getProductByCategory(item?.Id);
    }
  };

  // hàm chọn tất cả danh mục
  const handleSelectAllData = async () => {
    let arrayData: any[] = [];
    let arrayDataWithoutChildren: any[] = [];
    let arrayDataWithChildren: any[] = [];
    setSelectTwo(!selectTwo);
    let categoriesWithoutChildren = dataCategory?.filter(
      (item: any) => !item?.Children || item?.Children?.length === 0,
    );
    categoriesWithoutChildren?.forEach((item: any) => {
      arrayDataWithoutChildren.push(item.Id);
    });
    if (arrayDataWithoutChildren.length > 0) {
      let allProducts: any[] = [];
      for (const categoryId of arrayDataWithoutChildren) {
        const response = await productDA.getListSimple({
          query: `@ShopId:{${shopInfo[0]?.Id}} @CategoryId: {${categoryId}}`,
        });
        if (response?.code === 200 && response?.data) {
          // Filter products without discount
          const productsWithoutDiscount = response.data.filter(
            (item: any) => !item?.Discount,
          );
          allProducts = [...allProducts, ...productsWithoutDiscount];
        }
      }
      arrayData = [...arrayData, ...allProducts];
    }

    // Filter categories with Children.length > 0
    let categoriesWithChildren = dataCategory?.filter(
      (item: any) => item?.Children?.length > 0,
    );
    // Add IDs of categories with children to arrayData
    categoriesWithChildren?.forEach((item: any) => {
      // Add children's IDs
      if (item.Children && item.Children.length > 0) {
        item.Children.forEach((child: any) => {
          arrayDataWithChildren.push(child.Id);
        });
      }
    });
    // Get products for each child category ID
    if (arrayDataWithChildren.length > 0) {
      for (const categoryId of arrayDataWithChildren) {
        const response = await productDA.getListSimple({
          query: `@ShopId:{${shopInfo[0]?.Id}} @CategoryId: {${categoryId}}`,
        });
        if (response?.code === 200 && response?.data) {
          // Filter products without discount
          const productsWithoutDiscount = response.data.filter(
            (item: any) => !item?.Discount,
          );
          arrayData = [...arrayData, ...productsWithoutDiscount];
        }
      }
    }
    if (arrayData.length > 0) {
      props.handleGetDataDiscount(arrayData);
    }
  };

  const handleGetDataDiscount = async (data: any) => {
    const arrayData: any[] = [];
    if (data && data?.Id) {
      arrayData.push(data);
      setDataDiscount(arrayData);
      props.handleGetDataDiscount(arrayData);
    }
  };

  return (
    <Pressable
      style={{
        width: '100%',
        height: Dimensions.get('window').height - 100,
        borderTopLeftRadius: 16,
        borderTopRightRadius: 16,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        position: 'relative',
      }}>
      <ScreenHeader
        backIcon
        title={'Chọn danh mục'}
        onBack={() => navigation.goBack()}
      />
      <KeyboardAvoidingView
        style={{flex: 1, position: 'relative'}}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'} // Adjust for iOS and Android
        keyboardVerticalOffset={Platform.OS === 'ios' ? 65 : 0} // Offset for iOS
      >
        <View
          style={{
            padding: 10,
            backgroundColor: '#fff',
          }}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 10,
            }}>
            <TextInput
              style={{
                flex: 1,
                backgroundColor: '#f0f0f0',
                borderRadius: 10,
                padding: 8,
                marginRight: 10,
                height: 20,
                color: 'black',
              }}
              placeholder="Search"
            />
            <TouchableOpacity>
              <Text
                style={{
                  color: '#007AFF',
                  fontSize: 16,
                }}>
                Đã chọn
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        <View>
          <TouchableOpacity
            style={{
              marginLeft: 16,
              ...TypoSkin.body3,

              borderBottomWidth: 1,
              borderBottomColor:
                ColorThemes.light.neutral_main_background_color,
              marginTop: 10,
              paddingBottom: 10,
            }}
            onPress={() => handleSelectAllData()}>
            <Text style={{color: ColorThemes.light.primary_main_color}}>
              Chọn tất cả danh mục
            </Text>
          </TouchableOpacity>
          <Pressable style={{height: '100%'}}>
            {dataCategory && dataCategory?.length > 0 ? (
              dataCategory?.map((item, index) => (
                <CartPromotion
                  key={`item-${index}`}
                  item={item}
                  index={index}
                  handleSelectCategory={handleSelectCategory}
                  handleSelectAllCategory={handleSelectAllCategory}
                  selectTwo={selectTwo}
                  getdataByMenuTwo={getdataByMenuTwo}
                  selectThree={selectThree}
                  handleGetDataDiscount={handleGetDataDiscount}
                />
              ))
            ) : (
              <View
                style={{
                  height: '100%',
                  alignItems: 'center',
                  justifyContent: 'center',
                  paddingBottom: 250,
                }}>
                <EmptyPage />
              </View>
            )}
          </Pressable>
        </View>
        <View
          style={{
            flex: 1,
            marginBottom: 10,
            position: 'absolute',
            bottom: 2,
            width: '100%',
          }}>
          <View
            style={{
              flexDirection: 'row',
              width: '100%',
              alignItems: 'center',
              justifyContent: 'space-around',
              position: 'absolute',
              bottom: 10,
            }}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => props.handleSelectAllData(ref)}>
              <Text style={styles.buttonText}>Đóng</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.confirmButton}
              onPress={() => handleSelectDiscount()}>
              <Text style={styles.buttonTextTwo}>Xác nhận</Text>
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </Pressable>
  );
});

const styles = StyleSheet.create({
  content: {
    flex: 1,
    alignItems: 'center',
  },
  PromotionMenu: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 16,
    paddingRight: 16,
    paddingTop: 20,
    paddingBottom: 4,
  },
  label: {
    ...TypoSkin.title3,
    marginRight: 26,
    fontWeight: '400',
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ColorThemes.light.error_border_color,
    paddingVertical: 5,
    paddingHorizontal: 15,
    borderRadius: 10,
    marginRight: 8,
  },
  updateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ColorThemes.light.success_border_color,
    paddingVertical: 5,
    paddingHorizontal: 15,
    borderRadius: 10,
  },
  buttonText: {
    color: ColorThemes.light.neutral_text_subtitle_color,
    fontSize: 12,
    marginLeft: 4,
  },
  buttonTextTwo: {
    color: ColorThemes.light.neutral_absolute_background_color,
    fontSize: 12,
    marginLeft: 4,
  },
  buttonTextSuccess: {
    color: ColorThemes.light.success_main_color,
    fontSize: 12,
    marginLeft: 4,
  },
  PromorionNabar: {
    backgroundColor: '#fff',
    marginLeft: 2,
    marginTop: 10,
    marginBottom: 10,
    maxHeight: 24,
  },
  activeButton: {
    backgroundColor: ColorThemes.light.primary_background,
    paddingVertical: 5,
    paddingHorizontal: 15,
    borderRadius: 20,
    marginLeft: 16,
  },
  button: {
    backgroundColor: '#f0f0f0',
    paddingVertical: 5,
    paddingHorizontal: 15,
    borderRadius: 20,
    marginLeft: 16,
  },
  buttonTextMenu: {
    color: ColorThemes.light.primary_sub_color,
    fontSize: 12,
    fontWeight: '400',
  },
  menuDetail: {
    flex: 1,
    padding: 10,
    width: '100%',
    backgroundColor: '#fff',
    marginLeft: 12,
    marginRight: 12,
    marginBottom: 12,
    marginTop: 12,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
  headerText: {
    fontWeight: 'bold',
    ...TypoSkin.body2,
    paddingLeft: 10,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  imageContainer: {
    marginRight: 30,
    marginLeft: 30,
  },
  image: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  text: {
    flex: 1,
    ...TypoSkin.body3,
    marginLeft: '3%',
  },
  textTwo: {
    flex: 1,
    ...TypoSkin.body3,
    marginLeft: '15%',
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  editIcon: {
    fontSize: 16,
    color: '#000',
    marginRight: 10,
  },
  checkIcon: {
    fontSize: 16,
    color: '#00cc00',
    marginRight: 10,
  },
  deleteIcon: {
    fontSize: 16,
    color: '#ff0000',
  },
  checkbox: {
    marginRight: 10,
  },
  buyButton: {
    backgroundColor: 'blue',
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 50,
    borderRadius: 30,
  },

  actionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  cancelButton: {
    backgroundColor: '#f0f0f0', // Màu xám nhạt cho nút "Đóng"
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: '#ccc',
    width: '45%',
    alignItems: 'center',
    color: 'white',
  },
  confirmButton: {
    backgroundColor: '#007AFF', // Màu xanh cho nút "Xác nhận"
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    width: '45%',
    alignItems: 'center',
    color: 'white',
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    paddingLeft: 10,
    paddingRight: 20,
    paddingTop: 10,
    paddingBottom: 10,
    height: 50,
    backgroundColor: 'red',
  },
  itemText: {
    fontSize: 20,
    color: 'black',
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default ShopPromortionComponent;
