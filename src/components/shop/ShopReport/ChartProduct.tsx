import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import { <PERSON><PERSON><PERSON>, LineChart } from 'react-native-chart-kit';
import { ScrollView } from 'react-native-gesture-handler';
import { FLoading } from 'wini-mobile-components';
import ProductDA from '../../../modules/Product/da';
import { useSelectorShopState } from '../../../redux/hook/shopHook ';

const { width } = Dimensions.get('window');
const ChartProduct = () => {
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [analysisData, setAnalysisData] = useState<number[]>([0, 0, 0, 0, 0, 0, 0]);
    const shopInfo = useSelectorShopState().data;

    const data = {
        labels: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"],
        datasets: [
            {
                data: analysisData.length > 0 ? analysisData : [0]
            }
        ]
    };

    const getanalysisDateWeek = async () => {
        const currentDate = dayjs();
        const productDA = new ProductDA();
        const startOfWeek = currentDate.startOf('week').valueOf(); // Thứ Hai
        const endOfWeek = currentDate.endOf('week').valueOf(); // Chủ Nhật
        console.log("check-shopInfo", shopInfo)
        let res = await productDA.getProducts(shopInfo[0]?.Id, 1)
        console.log("check-res", res)
        if (res?.code === 200) {
            let dataDay = res?.data.filter((item: any) => Number(dayjs(item?.DateCreated).valueOf()) >= Number(startOfWeek) && Number(dayjs(item?.DateCreated).valueOf()) < Number(endOfWeek))
            console.log("check-dataDay", dataDay)

        }
        setIsLoading(false)
    }
    useEffect(() => {
        setIsLoading(true)
        getanalysisDateWeek()
    }, [])

    return (
        <ScrollView style={styles.container}>
            <FLoading visible={isLoading} />
            <View style={styles.header}>
                <TouchableOpacity >
                    <Text style={styles.titleOne}>Income</Text>
                </TouchableOpacity>
                <TouchableOpacity >
                    <Text style={styles.titleTwo}>Spend</Text>
                </TouchableOpacity>
            </View>
            <ScrollView style={styles.chartContainer} horizontal={true}>
                <BarChart
                    data={data}
                    width={600}
                    height={400}
                    chartConfig={{
                        backgroundColor: '#fff',
                        backgroundGradientFrom: '#fff',
                        backgroundGradientTo: '#fff',
                        decimalPlaces: 0,
                        color: (opacity = 1) => `rgba(0, 123, 255, ${opacity})`,
                        labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
                        style: {
                            borderRadius: 8,
                        },
                        propsForDots: {
                            r: '6',
                            strokeWidth: '2',
                            stroke: '#007bff',
                        },
                        fillShadowGradient: '#e6f0fa',
                        fillShadowGradientOpacity: 0.7,
                    }}
                    style={{
                        marginVertical: 8,
                        borderRadius: 8,
                    }}
                    withInnerLines={true}
                    yAxisLabel=""
                    yAxisSuffix="SP"
                />
            </ScrollView>
            <View style={styles.header}>
                <TouchableOpacity >
                    <Text style={styles.titleOne}>Month</Text>
                </TouchableOpacity>
                <TouchableOpacity >
                    <Text style={styles.titleTwo}>Year</Text>
                </TouchableOpacity>
            </View>
            <ScrollView style={styles.chartContainer} horizontal={true}>
                <LineChart
                    data={data}
                    width={700}
                    height={400}
                    chartConfig={{
                        backgroundColor: '#fff',
                        backgroundGradientFrom: '#fff',
                        backgroundGradientTo: '#fff',
                        decimalPlaces: 0,
                        color: (opacity = 1) => `rgba(0, 123, 255, ${opacity})`,
                        labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
                        style: {
                            borderRadius: 8,
                        },
                        propsForDots: {
                            r: '3',
                            strokeWidth: '3',
                            stroke: '#007bff',
                        },
                        fillShadowGradient: '#e6f0fa',
                        fillShadowGradientOpacity: 0.7,
                    }}
                    style={{
                        marginVertical: 8,
                        borderRadius: 8,
                    }}
                    withInnerLines={true}
                    yAxisLabel=""
                    yAxisSuffix="/SP"
                />
            </ScrollView>

        </ScrollView >
    );
};

const styles = StyleSheet.create({
    container: {
        backgroundColor: '#fff',
        borderRadius: 8,
        height: 700
    },
    header: {
        flexDirection: 'row',
        alignContent: "center",
        justifyContent: 'space-around',
        margin: "auto",
        marginTop: 2,
    },
    titleOne: {
        fontSize: 14,
        fontWeight: 'bold',
        borderRightWidth: 1,
        padding: 7,
        margin: "auto",
        width: 119.3,
        height: 29.82,
        backgroundColor: "blue",
        borderTopLeftRadius: 50,
        borderBottomLeftRadius: 50,
        textAlign: "center",
        color: "white",

    },

    titleTwo: {
        fontSize: 14,
        fontWeight: 'bold',
        padding: 7,
        margin: "auto",
        borderTopRightRadius: 50,
        borderBottomRightRadius: 50,
        textAlign: "center",
        color: "blue",
        backgroundColor: "#D9D9D9",
        width: 119.3,
        height: 29.82,
    },
    chartContainer: {
        padding: 10,
    },

});

export default ChartProduct;