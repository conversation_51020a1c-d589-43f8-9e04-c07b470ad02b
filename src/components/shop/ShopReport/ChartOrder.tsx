import React, { useEffect, useState } from 'react';
import { View, Text, Dimensions, StyleSheet } from 'react-native';
import { Pie<PERSON>hart } from 'react-native-chart-kit';
import { ScrollView } from 'react-native-gesture-handler';
import { useSelectorOrderState } from '../../../redux/hook/orderHook ';
import { FLoading } from 'wini-mobile-components';

// Get screen width for responsive chart size
const screenWidth = Dimensions.get('window').width;
const ChartOrder = () => {
    const OrderInfo = useSelectorOrderState().data;
    const [percent, setPercent] = useState<any>({
        CancelOrder: 0,
        DoneOrder: 0,
        NewOrder: 0,
        ProcessOrder: 0
    });
    const [isLoading, setIsLoading] = useState<boolean>(false);

    // Sample data for the pie chart
    const data = [
        {
            name: 'Đơn hủy',
            value: OrderInfo?.CancelOrder?.number,
            color: '#FF6347', // Tomato
            legendFontColor: '#7F7F7F',
            legendFontSize: 12,
        },
        {
            name: '<PERSON><PERSON>n hoàn thành',
            value: OrderInfo?.DoneOrder?.number,
            color: '#4682B4', // SteelBlue
            legendFontColor: '#7F7F7F',
            legendFontSize: 12,
        },
        {
            name: 'Đơn tạo mới',
            value: OrderInfo?.NewOrder?.number,
            color: '#FFD700', // Gold
            legendFontColor: '#7F7F7F',
            legendFontSize: 12,
        },
        {
            name: 'Đơn đang xử lý',
            value: OrderInfo?.ProcessOrder?.number,
            color: '#6A5ACD', // SlateBlue
            legendFontColor: '#7F7F7F',
            legendFontSize: 12,
        },
    ];

    useEffect(() => {
        let all = (OrderInfo?.CancelOrder?.number + OrderInfo?.DoneOrder?.number + OrderInfo?.NewOrder?.number + OrderInfo?.ProcessOrder?.number)
        setPercent({
            CancelOrder: Math.round(Number(OrderInfo?.CancelOrder?.number) / Number(all) * 100),
            DoneOrder: Math.round(Number(OrderInfo?.DoneOrder?.number) / Number(all) * 100),
            NewOrder: Math.round(Number(OrderInfo?.NewOrder?.number) / Number(all) * 100),
            ProcessOrder: Math.round(Number(OrderInfo?.ProcessOrder?.number) / Number(all) * 100)
        })
    }, [OrderInfo])


    return (
        <ScrollView >
            <FLoading visible={isLoading} />
            <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center', backgroundColor: 'white' }}>
                <PieChart
                    data={data}
                    width={screenWidth - 40} // Responsive width
                    height={220}
                    chartConfig={{
                        color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`, // Default color for text/labels
                    }}
                    accessor="value" // Key for the data values
                    backgroundColor="transparent"
                    paddingLeft="15"
                    absolute // Shows absolute values on the chart
                />
            </View>
            <View >
                <Text style={styles.headerText}>Details</Text>
            </View>
            <ScrollView >
                <View style={styles.containerItem}>
                    <View style={styles.content}>
                        {/* Left side - Percentage with circle */}
                        <View style={styles.leftSection}>
                            <View style={styles.circle} />
                            <Text style={styles.percentage}>{percent.CancelOrder}%</Text>
                        </View>

                        {/* Right side - Text and Amount */}
                        <View style={styles.rightSection}>
                            <Text style={styles.documentText}>Đơn hàng hủy</Text>
                            <Text style={styles.amount}>{OrderInfo?.CancelOrder?.number} đơn</Text>
                        </View>
                    </View>
                </View>
                <View style={styles.containerItem}>
                    <View style={styles.content}>
                        {/* Left side - Percentage with circle */}
                        <View style={styles.leftSection}>
                            <View style={styles.circleTwo} />
                            <Text style={styles.percentage}>{percent.DoneOrder}%</Text>
                        </View>

                        {/* Right side - Text and Amount */}
                        <View style={styles.rightSection}>
                            <Text style={styles.documentText}>Đơn hoàn thành</Text>
                            <Text style={styles.amount}>{OrderInfo?.DoneOrder?.number} đơn</Text>
                        </View>
                    </View>
                </View>
                <View style={styles.containerItem}>
                    <View style={styles.content}>
                        {/* Left side - Percentage with circle */}
                        <View style={styles.leftSection}>
                            <View style={styles.circleThree} />
                            <Text style={styles.percentage}>{percent.NewOrder}%</Text>
                        </View>

                        {/* Right side - Text and Amount */}
                        <View style={styles.rightSection}>
                            <Text style={styles.documentText}>Đơn tạo mới</Text>
                            <Text style={styles.amount}>{OrderInfo?.NewOrder?.number} đơn</Text>
                        </View>
                    </View>
                </View>
                <View style={styles.containerItem}>
                    <View style={styles.content}>
                        {/* Left side - Percentage with circle */}
                        <View style={styles.leftSection}>
                            <View style={styles.circleFour} />
                            <Text style={styles.percentage}>{percent.ProcessOrder}%</Text>
                        </View>

                        {/* Right side - Text and Amount */}
                        <View style={styles.rightSection}>
                            <Text style={styles.documentText}>Đơn đang xử lý</Text>
                            <Text style={styles.amount}>{OrderInfo?.ProcessOrder?.number} đơn</Text>
                        </View>
                    </View>
                </View>

            </ScrollView>


        </ScrollView >
    );
};

const styles = StyleSheet.create({
    container: {
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'row',
        borderWidth: 1,
        height: 87
    },
    containerItem: {
        backgroundColor: '#FFFFFF',
        borderRadius: 12,
        padding: 15,
        marginLeft: 16,
        marginRight: 16,
        marginBottom: 14,
        marginTop: 13,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
        elevation: 5, // For Android shadow
        borderWidth: 1,
        borderColor: '#F3F4F6',
    },

    headerText: {
        color: '#8F90FF', // Purple color
        fontSize: 18,
        fontWeight: '500',
    },
    content: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: "100%"
    },
    leftSection: {
        flex: 0.2,
        flexDirection: 'row',
        alignItems: 'center',
    },
    circle: {
        width: 27,
        height: 27,
        backgroundColor: '#EF4444', // Red color
        borderRadius: 50,
        marginRight: 5,
    },
    circleTwo: {
        width: 27,
        height: 27,
        backgroundColor: '#370665',
        borderRadius: 50,
        marginRight: 5,
    },
    circleThree: {
        width: 27,
        height: 27,
        backgroundColor: '#35589A',
        borderRadius: 50,
        marginRight: 5,
    },
    circleFour: {
        width: 27,
        height: 27,
        backgroundColor: '#00952A',
        borderRadius: 50,
        marginRight: 5,
    },
    percentage: {
        color: '#2563EB', // Blue color
        fontSize: 24,
        fontWeight: 'bold',
    },
    rightSection: {
        flex: 0.7,
        marginLeft: 20,
    },
    documentText: {
        color: '#2563EB', // Blue color
        fontSize: 16,
        fontWeight: '500',
        marginBottom: 4,
    },
    amount: {
        color: '#2563EB', // Blue color
        fontSize: 22,
        fontWeight: 'bold',
    },

});

export default ChartOrder;