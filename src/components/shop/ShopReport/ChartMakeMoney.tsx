import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import {LineChart} from 'react-native-chart-kit';
import {ScrollView} from 'react-native-gesture-handler';
import {OrderDA} from '../../../modules/order/orderDA';
import {useSelectorShopState} from '../../../redux/hook/shopHook ';
import {StatusOrder} from '../../../Config/Contanst';
import dayjs from 'dayjs';
const weekOfYear = require('dayjs/plugin/weekOfYear');
const isoWeek = require('dayjs/plugin/isoWeek');
dayjs.extend(weekOfYear);
dayjs.extend(isoWeek);
import {FLoading} from 'wini-mobile-components';
const {width} = Dimensions.get('window');

const ChartMoney = () => {
  const [dateRange] = useState('29 May 2025'); // Cập nhật ng<PERSON>y hiện tại
  const [selectedTime, setSelectedTime] = useState('1H');
  const [analysisData, setAnalysisData] = useState<number[]>([]);
  const [labels, setLabels] = useState<string[]>([]);
  const [money, setMoney] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(false);
  const orderDA = new OrderDA();
  const shopInfo = useSelectorShopState().data;

  const timeOptions = ['1H', '1D', '1W', '1M', '6M', '1Y'];
  const currentHour = dayjs().format('HH');
  const currentDay = dayjs().format('dddd');
  let getanalysisDataHour = async () => {
    let res = await orderDA.getOrderByShopId(
      shopInfo[0]?.Id,
      StatusOrder.success,
    );
    console.log('check-res', res);
    if (res?.code === 200) {
      let dataHours = res?.data.filter(
        (item: any) => dayjs(item?.DateCreated).format('HH') == currentHour,
      );
      console.log('check-dataHours', dataHours, currentHour);
      let MoneyHour = [
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
      ];
      for (let item of dataHours) {
        // Get the minute from the timestamp
        const minute = Number(dayjs(item?.DateCreated).format('mm'));
        // Calculate the index based on 5-minute intervals (0-4, 5-9, etc.)
        MoneyHour[minute] += Number(item?.NumberQuality) * Number(item?.Value);
      }
      console.log('check-MoneyHour', MoneyHour);
      if (MoneyHour.length > 0) {
        setAnalysisData(MoneyHour);
      } else {
        setAnalysisData([0]);
      }
      setIsLoading(false);
    }
  };
  let getanalysisDataDay = async () => {
    let res = await orderDA.getOrderByShopId(
      shopInfo[0]?.Id,
      StatusOrder.success,
    );
    console.log('check-currentDay', currentDay);
    if (res?.code === 200) {
      let dataDay = res?.data.filter(
        (item: any) => dayjs(item?.DateCreated).format('dddd') == currentDay,
      );
      let MoneyDay = [
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
      ];
      for (let item of dataDay) {
        let hour = Number(dayjs(item?.DateCreated).format('HH'));
        MoneyDay[hour] += Number(item?.NumberQuality) * Number(item?.Value);
      }
      console.log('check-MoneyDay', MoneyDay);
      if (MoneyDay.length > 0) {
        setMoney(MoneyDay.reduce((total, num) => total + num, 0));
        setAnalysisData(MoneyDay);
      } else {
        setAnalysisData([0]);
      }
      setIsLoading(false);
    }
  };
  let getanalysisDateWeek = async () => {
    const currentDate = dayjs();
    const startOfWeek = currentDate.startOf('week');
    const endOfWeek = currentDate.endOf('week');

    let res = await orderDA.getOrderByShopId(
      shopInfo[0]?.Id,
      StatusOrder.success,
    );

    if (res?.code === 200) {
      // Initialize array with 7 zeros for each day of the week
      let MoneyDay = [0, 0, 0, 0, 0, 0, 0];

      let dataDay = res?.data.filter(
        (item: any) =>
          dayjs(item?.DateCreated).isAfter(startOfWeek) &&
          dayjs(item?.DateCreated).isBefore(endOfWeek),
      );

      console.log('check-dataDay', dataDay);

      for (let item of dataDay) {
        // Get the day of week (0-6, where 0 is Sunday)
        const dayOfWeek = dayjs(item?.DateCreated).day();
        console.log('check-dayOfWeek', dayOfWeek);
        // Add the order amount to the corresponding day
        MoneyDay[dayOfWeek - 1] +=
          Number(item?.NumberQuality) * Number(item?.Value);
      }

      // Calculate total money for the week
      const totalMoney = MoneyDay.reduce((total, num) => total + num, 0);
      setMoney(totalMoney);

      if (MoneyDay.length > 0) {
        setAnalysisData(MoneyDay);
      } else {
        setAnalysisData([0]);
      }
      setIsLoading(false);
    }
  };
  let getanalysisDateMonth = async () => {
    const currentDate = dayjs();
    const startOfMonth = currentDate.startOf('month');
    const endOfMonth = currentDate.endOf('month');
    const daysInMonth = endOfMonth.date(); // Get number of days in current month

    let res = await orderDA.getOrderByShopId(
      shopInfo[0]?.Id,
      StatusOrder.success,
    );
    if (res?.code === 200) {
      let dataDay = res?.data.filter(
        (item: any) =>
          Number(dayjs(item?.DateCreated).valueOf()) >= Number(startOfMonth) &&
          Number(dayjs(item?.DateCreated).valueOf()) < Number(endOfMonth),
      );

      // Initialize array with zeros for each day of the month
      let MoneyMonth = new Array(daysInMonth).fill(0);

      for (let item of dataDay) {
        const dayOfMonth = dayjs(item?.DateCreated).date() - 1; // Get day of month (0-based index)
        MoneyMonth[dayOfMonth] +=
          Number(item?.NumberQuality) * Number(item?.Value);
      }

      if (MoneyMonth.length > 0) {
        setMoney(MoneyMonth.reduce((total, num) => total + num, 0));
        setAnalysisData(MoneyMonth);
      } else {
        setAnalysisData([0]);
      }
      setIsLoading(false);
    }
  };

  let getanalysisDateSixMonth = async (data: any[]) => {
    let res = await orderDA.getOrderByShopId(
      shopInfo[0]?.Id,
      StatusOrder.success,
    );
    if (res?.code === 200) {
      let dataDay = res?.data.filter(
        (item: any) =>
          Number(dayjs(item?.DateCreated).valueOf()) >
            Number(dayjs(data[0].startOfMonth).valueOf()) &&
          Number(dayjs(item?.DateCreated).valueOf()) <
            Number(dayjs(data[data.length - 1].endOfMonth).valueOf()),
      );

      // Initialize array with 6 zeros for each month
      let MoneyMonth = new Array(6).fill(0);

      for (let item of dataDay) {
        const itemDate = dayjs(item?.DateCreated);
        const monthIndex = 5 - (dayjs().month() - itemDate.month());
        if (monthIndex >= 0 && monthIndex < 6) {
          MoneyMonth[monthIndex] +=
            Number(item?.NumberQuality) * Number(item?.Value);
        }
      }

      if (MoneyMonth.length > 0) {
        setMoney(MoneyMonth.reduce((total, num) => total + num, 0));
        setAnalysisData(MoneyMonth);
      } else {
        setAnalysisData([0]);
      }
      setIsLoading(false);
    }
  };
  let getanalysisDateOneYear = async (data: any) => {
    let res = await orderDA.getOrderByShopId(
      shopInfo[0]?.Id,
      StatusOrder.success,
    );
    if (res?.code === 200) {
      let dataDay = res?.data.filter(
        (item: any) =>
          Number(dayjs(item?.DateCreated).valueOf()) >
            Number(dayjs(data.firstDay).valueOf()) &&
          Number(dayjs(item?.DateCreated).valueOf()) <
            Number(dayjs(data.lastDay).valueOf()),
      );

      // Initialize array with 12 zeros for each month
      let MoneyMonth = new Array(12).fill(0);

      for (let item of dataDay) {
        const monthIndex = dayjs(item?.DateCreated).month(); // Get month index (0-11)
        MoneyMonth[monthIndex] +=
          Number(item?.NumberQuality) * Number(item?.Value);
      }

      if (MoneyMonth.length > 0) {
        setMoney(MoneyMonth.reduce((total, num) => total + num, 0));
        setAnalysisData(MoneyMonth);
      } else {
        setAnalysisData([0]);
      }
      setIsLoading(false);
    }
  };
  let dataArray = [0];
  const chartDataH = {
    labels: labels,
    datasets: [
      {
        data: analysisData.length > 0 ? analysisData : dataArray,
      },
    ],
  };

  useEffect(() => {
    setIsLoading(true);
    setLabels([
      `${currentHour}:00`,
      `${currentHour}:05`,
      `${currentHour}:10`,
      `${currentHour}:15`,
      `${currentHour}:20`,
      `${currentHour}:25`,
      `${currentHour}:30`,
      `${currentHour}:35`,
      `${currentHour}:40`,
      `${currentHour}:45`,
      `${currentHour}:50`,
      `${currentHour}:55`,
    ]);
    getanalysisDataHour();
  }, []);
  useEffect(() => {
    if (selectedTime == '1H') {
      getanalysisDataHour();

      setLabels([
        `${currentHour}:00`,
        `${currentHour}:05`,
        `${currentHour}:10`,
        `${currentHour}:15`,
        `${currentHour}:20`,
        `${currentHour}:25`,
        `${currentHour}:30`,
        `${currentHour}:35`,
        `${currentHour}:40`,
        `${currentHour}:45`,
        `${currentHour}:50`,
        `${currentHour}:55`,
      ]);
    }
    if (selectedTime == '1D') {
      getanalysisDataDay();
      setLabels([
        '00:00',
        '01:00',
        '02:00',
        '03:00',
        '04:00',
        '05:00',
        '06:00',
        '07:00',
        '08:00',
        '09:00',
        '10:00',
        '11:00',
        '12:00',
        '13:00',
        '14:00',
        '15:00',
        '16:00',
        '17:00',
        '18:00',
        '19:00',
        '20:00',
        '21:00',
        '22:00',
        '23:00',
      ]);
    }
    if (selectedTime == '1W') {
      getanalysisDateWeek();
      setLabels([
        'Thứ hai',
        'Thứ ba',
        'Thứ tư',
        'Thứ năm',
        'Thứ sáu',
        'Thứ bảy',
        'Chủ nhật',
      ]);
    }
    if (selectedTime == '1M') {
      getanalysisDateMonth();
      const year = dayjs().year();
      const month = dayjs().month();
      const allDays = [];
      const lastDay = new Date(year, month + 1, 0); // Ngày 0 của tháng tiếp theo = ngày cuối tháng hiện tại
      const daysInMonth = lastDay.getDate(); // Số ngày trong tháng
      for (let day = 1; day <= daysInMonth; day++) {
        const date = new Date(year, month, day);
        const formattedDate = `${String(date.getDate()).padStart(
          2,
          '0',
        )}/${String(date.getMonth() + 1).padStart(
          2,
          '0',
        )}/${date.getFullYear()}`;
        allDays.push(formattedDate);
      }
      setLabels(allDays);
    }
    if (selectedTime == '6M') {
      // Lấy ngày hiện tại
      const currentDate = dayjs(); // 2025-06-10
      // Lấy tháng hiện tại
      const currentMonth = currentDate.month(); // Tháng 6 (Day.js đếm từ 0, nên là 5)
      const currentYear = currentDate.year(); // 2025
      // Tạo mảng để lưu thông tin 6 tháng gần nhất
      const recentMonths = [];
      // Lặp để lấy 6 tháng trước đó, bao gồm tháng hiện tại
      for (let i = 5; i >= 0; i--) {
        const pastDate = currentDate.subtract(i, 'month');
        const monthInfo = {
          month: pastDate.month() + 1, // Cộng 1 vì Day.js đếm tháng từ 0
          year: pastDate.year(),
          startOfMonth: pastDate.startOf('month').format('YYYY-MM-DD'),
          endOfMonth: pastDate.endOf('month').format('YYYY-MM-DD'),
        };
        recentMonths.push(monthInfo);
      }

      // In thông tin 6 tháng gần nhất
      // console.log('Thông tin 6 tháng gần nhất:');
      let dataMounth: any[] = [];
      recentMonths.forEach((month, index) => {
        console.log(`${month.month}/${month.year}:`);
        dataMounth.push({
          startOfMonth: month.startOfMonth,
          endOfMonth: month.endOfMonth,
        });
      });
      getanalysisDateSixMonth(dataMounth);
      setLabels(recentMonths.map(month => `${month.month}/${month.year}`));
    }
    if (selectedTime == '1Y') {
      const currentDate = dayjs(); // 2025-06-10

      // Lấy năm hiện tại
      const currentYear = currentDate.year();
      // Tạo mảng để lưu thông tin các tháng
      const monthsInYear = [];

      // Lặp qua 12 tháng (Day.js đếm tháng từ 0 đến 11)
      for (let i = 0; i < 12; i++) {
        const date = dayjs(`${currentYear}-${i + 1}-01`); // Tạo ngày đầu của mỗi tháng
        const monthInfo = {
          month: i + 1, // Số tháng (cộng 1 vì Day.js đếm từ 0)
          monthName: date.format('MMMM'), // Tên tháng (bằng tiếng Anh)
          startOfMonth: date.startOf('month').format('YYYY-MM-DD'),
          endOfMonth: date.endOf('month').format('YYYY-MM-DD'),
        };
        monthsInYear.push(monthInfo);
      }
      console.log('check-monthsInYear', monthsInYear);
      getanalysisDateOneYear({
        firstDay: monthsInYear[0].startOfMonth,
        lastDay: monthsInYear[monthsInYear.length - 1].endOfMonth,
      });
      setLabels(monthsInYear.map(month => `${month.month}`));
    }
  }, [selectedTime]);

  return (
    <View style={styles.container}>
      <FLoading visible={isLoading} />
      <Text style={styles.title}>Doanh thu hôm nay</Text>
      <Text style={styles.revenue}>{money} VNĐ</Text>
      <Text style={styles.dateRange}>{dayjs().format('DD/MM/YYYY HH:mm')}</Text>
      <ScrollView style={styles.chartContainer} horizontal={true}>
        <LineChart
          data={chartDataH}
          width={selectedTime == '1M' ? 2300 : 2000}
          height={400}
          chartConfig={{
            backgroundColor: '#fff',
            backgroundGradientFrom: '#fff',
            backgroundGradientTo: '#fff',
            decimalPlaces: 0,
            color: (opacity = 1) => `rgba(0, 123, 255, ${opacity})`,
            labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
            style: {
              borderRadius: 8,
            },
            propsForDots: {
              r: '6',
              strokeWidth: '2',
              stroke: '#007bff',
            },
            fillShadowGradient: '#e6f0fa',
            fillShadowGradientOpacity: 0.7,
            count: 5,
          }}
          bezier
          style={{
            marginVertical: 8,
            borderRadius: 8,
            paddingLeft: 60,
          }}
          withInnerLines={false}
          yAxisLabel=""
          yAxisSuffix="VNĐ"
        />
      </ScrollView>
      <View style={styles.timeOptions}>
        {timeOptions.map(option => (
          <TouchableOpacity
            key={option}
            style={[
              styles.timeButton,
              selectedTime === option && styles.selectedTime,
            ]}
            onPress={() => setSelectedTime(option)}>
            <Text style={styles.timeText}>{option}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#fff',
    borderRadius: 8,
  },
  tabContainer: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  tab: {
    padding: 4,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
    marginLeft: 5,
    marginRight: 5,
  },
  tabAction: {
    padding: 4,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
    marginLeft: 5,
    marginRight: 5,
    backgroundColor: '#007bff',
    borderRadius: 10,
  },
  tabText: {
    fontSize: 20,
    color: 'blue',
    padding: 3,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  revenue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#007bff',
    marginBottom: 8,
  },
  dateRange: {
    fontSize: 14,
    color: '#007bff',
    marginBottom: 16,
  },
  chartContainer: {
    marginBottom: 16,
  },
  timeOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  timeButton: {
    padding: 8,
    borderRadius: 4,
  },
  selectedTime: {
    backgroundColor: '#5264BE',
    color: 'white',
  },
  timeText: {
    color: '#007bff',
    fontWeight: 'bold',
  },
});

export default ChartMoney;
