import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  Pressable,
} from 'react-native';
import {FlatList, ScrollView} from 'react-native-gesture-handler';
import {FLoading, Winicon} from 'wini-mobile-components';
import {Title} from '../../../Config/Contanst';
import {OrderData} from '../../../mock/shopData';
import ConfigAPI from '../../../Config/ConfigAPI';
import {TypoSkin} from '../../../assets/skin/typography';
import {ColorThemes} from '../../../assets/skin/colors';

const CardOrder = (
  {item, index}: {item: any; index: number},
  action: string,
  loading: boolean,
  handleUpdateStatusProcessOrder: (item: any, type?: string) => void,
  handleUpdateStatusProcessOrderCancel: (item: any) => void,
) => {
  return (
    <Pressable key={`key ${index}`}>
      <FLoading visible={loading} />
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.orderId}>Đơn hàng {item?.Id}</Text>
          <Text style={styles.status}>
            {item.Status == 1 && 'Đang thực hiện'}
            {item.Status == 2 && 'Đang thực hiện'}
            {item.Status == 3 && 'Hoàn thành'}
            {item.Status == 4 && 'Hủy'}
          </Text>
        </View>
        {/* Thông tin sản phẩm */}
        <View style={styles.productContainer}>
          <Image
            source={{uri: ConfigAPI.urlImg + item.Img}}
            style={styles.productImage}
          />
          <View style={styles.productInfo}>
            <Text style={styles.productName}>{item.Name}</Text>
            <Text style={styles.productDetails}>
              <Text>Mã sản phẩm :</Text>
              <Text>
                <Text
                  style={{
                    color: ColorThemes.light.neutral_text_title_color,
                    fontWeight: '500',
                    ...TypoSkin.title4,
                  }}>
                  86988465655656
                </Text>{' '}
                - <Text>{item?.Property}</Text>
              </Text>
            </Text>
            <Text style={styles.productDetails}>
              <Text style={styles.productName}>Hoàn tiền :</Text>
              <Text style={styles.productDetails}>
                <Text>(kh: 3850đ)-(f1:32000đ)</Text>
              </Text>
            </Text>
            <Text style={styles.productPrice}>
              <Text style={styles.productName}>Giá:</Text>
              <Text style={{color: ColorThemes.light.error_main_color}}>
                {item.Value} VNĐ
              </Text>
            </Text>
          </View>
        </View>

        {/* Số lượng và tổng tiền */}
        <View style={styles.quantityTotal}>
          <TouchableOpacity style={styles.quantityButton}>
            <Text style={styles.quantityText}>
              <Text
                style={{
                  ...TypoSkin.title3,
                  color: ColorThemes.light.neutral_text_title_color,
                }}>
                Xem thêm
              </Text>
              <Winicon
                src="color/arrows/arrow-sm-down"
                size={13}
                color={ColorThemes.light.neutral_text_title_color}
              />
            </Text>
          </TouchableOpacity>
          <View style={styles.quantityDetail}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Text style={styles.quantity}>
                Tổng hoàn ({item.NumberQuality ? item.NumberQuality : 0} sản
                phẩm):
              </Text>
              <Text style={styles.money}>
                {Number(item.Value * item.NumberQuality)} VNĐ
              </Text>
            </View>
          </View>
          <View style={styles.quantityDetail}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Text style={styles.quantity}>
                Tổng tiền ({item.NumberQuality ? item.NumberQuality : 0} sản
                phẩm):
              </Text>
              <Text style={styles.money}>
                {Number(item.Value * item.NumberQuality)} VNĐ
              </Text>
            </View>
          </View>
        </View>
        {action !== Title.Cancel && (
          <View style={styles.button}>
            <View style={{flexDirection: 'row', gap: 10}}></View>
            <View style={{flexDirection: 'row', gap: 10}}>
              <TouchableOpacity
                style={styles.confirmButton}
                onPress={() => handleUpdateStatusProcessOrder(item)}>
                <Text style={styles.confirmButtonText}>
                  {action ? action : ''}
                </Text>
              </TouchableOpacity>
              {item?.Status == 2 && (
                <TouchableOpacity
                  style={styles.confirmButton}
                  onPress={() => handleUpdateStatusProcessOrderCancel(item)}>
                  <Text style={styles.confirmButtonText}>Xác nhận hủy</Text>
                </TouchableOpacity>
              )}
              {item?.Status == 3 && (
                <TouchableOpacity style={styles.confirmButton}>
                  <Text style={styles.confirmButtonText}>
                    Yêu cầu hoàn hàng
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        )}
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#ddd',
    padding: 10,
    margin: 10,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2, // Bóng cho Android
    marginTop: 6,
  },
  header: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  orderId: {
    flex: 0.8,
    ...TypoSkin.title2,
    fontWeight: '500',
    color: ColorThemes.light.neutral_text_title_color,
  },
  status: {
    flex: 0.3,
    textAlign: 'right',
    ...TypoSkin.title3,
    color: ColorThemes.light.error_main_color,
  },
  productContainer: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  productImage: {
    borderWidth: 5,
    borderRadius: 50,
    width: 60,
    height: 60,
    marginRight: 16,
    borderColor: '#F8F8FF',
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    ...TypoSkin.title4,
    fontWeight: '500',
    color: ColorThemes.light.neutral_text_title_color,
  },
  productDetails: {
    ...TypoSkin.title4,
    color: ColorThemes.light.neutral_text_title_color,
    marginVertical: 2,
  },
  productPrice: {
    ...TypoSkin.title4,
    color: ColorThemes.light.neutral_text_title_color,
  },
  quantityTotal: {
    flexDirection: 'column',
    justifyContent: 'space-evenly',
    alignItems: 'center',
    marginBottom: 10,
  },
  quantityButton: {
    borderRadius: 5,
    paddingHorizontal: 10,
    paddingVertical: 5,
  },
  quantityText: {
    fontSize: 14,
    color: '#555',
    display: 'flex',
    gap: 1,
  },
  quantityDetail: {
    height: 30,
    width: '100%',
    alignItems: 'flex-end',
  },
  quantity: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    display: 'flex',
    justifyContent: 'flex-end',
  },
  money: {
    ...TypoSkin.title3,
    color: 'red',
  },
  button: {
    minHeight: 40,
    width: '100%',
    display: 'flex',
    alignItems: 'flex-end',
  },
  confirmButton: {
    backgroundColor: '#FFA500',
    width: 154,
    borderRadius: 50,
    paddingVertical: 10,
    alignItems: 'center',
  },
  confirmButtonText: {
    ...TypoSkin.title3,
    color: ColorThemes.light.error_main_color,
    fontWeight: 'bold',
  },
});

export default CardOrder;
