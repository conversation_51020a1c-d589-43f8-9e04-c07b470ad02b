import React, { use, useEffect, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Pressable,
} from 'react-native';
import { FlatList } from 'react-native-gesture-handler';
import { ListTile, Winicon } from 'wini-mobile-components';
import { useNavigation } from '@react-navigation/native';
import ListItemCard from '../card/CartProductCreate';
import { DataController } from '../../../base/baseController';
import { ListItemProps } from '../../dto/dto';
import { useSelector } from 'react-redux';
import { RootState } from '../../../redux/store/store';

const ListItem = (props: ListItemProps) => {
  const {
    setSelecChildID,
    setSelecChildName,
    selectItemChild,
    setSelectItemChild,
  } = props;
  const CategoryController = new DataController('Category');
  const [isSelected, setIsSelected] = useState('');
  const [data, setData] = useState<any[]>();
  const { data: dataCategory } = useSelector(
    (state: RootState) => state.category,
  );

  useEffect(() => {
    console.log('check-dataCategory', dataCategory);
  }, [dataCategory]);

  const handleSelect = (item: any) => {
    setSelecChildID(item?.Id);
    setSelecChildName(item?.Name);
    if (item?.Id !== isSelected) {
      setIsSelected(item.Id);
    } else {
      setIsSelected('');
    }
  };

  useEffect(() => {
    if (selectItemChild) {
      console.log('check-selectItemChild', selectItemChild);

      setData(selectItemChild?.Children);
    }
  }, [selectItemChild]);

  useEffect(() => {
    console.log('check-data', data);
  }, [data]);

  return (
    <FlatList
      data={data ? data : dataCategory}
      style={{ flex: 1, marginBottom: 50 }}
      keyExtractor={(item, i) => `${i} ${item.id}`}
      renderItem={({ item, index }) =>
        ListItemCard(
          { item, index },
          isSelected,
          handleSelect,
          selectItemChild,
          setSelectItemChild,
        )
      }
    />
  );
};

export default ListItem;
