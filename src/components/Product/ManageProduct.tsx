import { useNavigation } from '@react-navigation/native';
import React, { useEffect, useState } from 'react';
import { View, Text, Image, TouchableOpacity, Switch, StyleSheet } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { FLoading, Winicon } from 'wini-mobile-components';
import { RootScreen } from '../../router/router';
import { Title, TypeMenuPorduct } from '../../Config/Contanst';
import MenuProduct from './MenuProduct';
import { listProductsData } from '../../mock/shopData';
import { DataController } from '../../base/baseController';
import { useSelectorCustomerState } from '../../redux/hook/customerHook';
import ProductDA from '../../modules/Product/da';
import { useSelectorShopState } from '../../redux/hook/shopHook ';
import { ListProducts } from '../dto/dto';
import { useDispatch } from 'react-redux';
import { ProductActions } from '../../redux/reducers/ProductReducer';
import { useSelectorProductState } from '../../redux/hook/productHook ';
import ManageProductDetail from './list/ListManageProductDetail';

const ManageItemProduct = () => {
    const navigation = useNavigation<any>();
    const dispatch = useDispatch<any>();
    const shopInfo = useSelectorShopState().data;
    const productInfo = useSelectorProductState().data;
    const [menu, setMenu] = useState<string>("Còn hàng");
    const [dataShop, setDataShop] = useState<any[]>([]);
    const [isloading, setIsloading] = useState<boolean>(false);



    return (
        <View style={styles.container}>
            <FLoading visible={isloading} />
            <MenuProduct
                setMenu={setMenu}
                menu={menu}
                data={productInfo}
            />
            <ManageProductDetail
                menu={menu}
                dataShop={productInfo}
            />
            <TouchableOpacity style={styles.buyButton} onPress={() => navigation.push(RootScreen.CreateNewProduct, { title: Title.CreateMyProduct })}>
                <Text style={styles.createButton}>Thêm sản phẩm mới</Text>
            </TouchableOpacity>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        margin: 10,
        position: "relative"
    },
    buyButton: {
        backgroundColor: 'blue',
        width: "80%",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        minHeight: 50,
        borderRadius: 30,
        margin: "auto",
        marginBottom: 20,
        position: "absolute",
        bottom: 0,
        left: "10%",

    },
    createButton: {
        color: '#fff',
        fontSize: 16,
        fontWeight: '500',
        textAlign: 'center',
    },

});

export default ManageItemProduct;