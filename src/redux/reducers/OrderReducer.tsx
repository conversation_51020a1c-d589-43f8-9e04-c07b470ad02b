import {useNavigation} from '@react-navigation/native';
import {
  Dispatch,
  PayloadAction,
  UnknownAction,
  createSlice,
} from '@reduxjs/toolkit';
import {OrderDA} from '../../modules/order/orderDA';
import {useSelectorCustomerState} from '../hook/customerHook';

interface OrderSimpleResponse {
  data?: any;
  onLoading?: boolean;
  type?: string;
}

const initState: OrderSimpleResponse = {
  data: undefined,
  onLoading: false,
};

export const orderSlice = createSlice({
  name: 'Order',
  initialState: initState,
  reducers: {
    handleActionsOrder: (state, action: PayloadAction<any>) => {
      switch (action.payload.type) {
        case 'GETINFORORDER':
          state.data = action.payload.data;
          break;
        case 'UPDATE':
          state.data = action.payload.data;
          break;
        default:
          break;
      }
      state.onLoading = false;
    },
    onFetching: state => {
      state.onLoading = true;
    },
  },
});

export const {handleActionsOrder, onFetching} = orderSlice.actions;

export default orderSlice.reducer;

export class OrderActions {
  static getInforOrder = (ShopID: string) => async (dispatch: Dispatch) => {
    const orderDA = new OrderDA();
    let data = {
      NewOrder: {
        number: 0,
        data: [],
      },
      ProcessOrder: {
        number: 0,
        data: [],
      },
      DoneOrder: {
        number: 0,
        data: [],
      },
      CancelOrder: {
        number: 0,
        data: [],
      },
    };

    // Get new orders with details
    const NewOrder = await orderDA.getOrderByShopId(ShopID, 1);
    if (NewOrder.code == 200) {
      data.NewOrder.number = NewOrder.data.length;
      data.NewOrder.data = NewOrder.data;
    }

    // Get process orders with details
    const ProcessOrder = await orderDA.getOrderByShopId(ShopID, 2);
    if (ProcessOrder.code == 200) {
      data.ProcessOrder.number = ProcessOrder.data.length;
      data.ProcessOrder.data = ProcessOrder.data;
    }

    // Get done orders with details
    const DoneProduct = await orderDA.getOrderByShopId(ShopID, 3);
    if (DoneProduct.code == 200) {
      data.DoneOrder.number = DoneProduct.data.length;
      data.DoneOrder.data = DoneProduct.data;
    }

    // Get cancel orders with details
    const CancelProduct = await orderDA.getOrderByShopId(ShopID, 4);
    if (CancelProduct.code == 200) {
      data.CancelOrder.number = CancelProduct.data.length;
      data.CancelOrder.data = CancelProduct.data;
    }

    dispatch(
      handleActionsOrder({
        type: 'GETINFORORDER',
        data: data,
      }),
    );
  };
}
