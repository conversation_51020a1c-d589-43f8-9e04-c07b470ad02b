import {createAsyncThunk} from '@reduxjs/toolkit';
import {showSnackbar, ComponentStatus} from 'wini-mobile-components';
import {DataController} from '../../base/baseController';
import {RootState} from '../store/store';
import {NewsEvent} from '../models/newsEvent';
import {BaseDA} from '../../base/BaseDA';
import ConfigAPI from '../../Config/ConfigAPI';
import {getImage} from './rootAction';

const fetchNewsEvents = createAsyncThunk<
  NewsEvent[],
  {page?: number; status?: number} | undefined,
  {state: RootState}
>('newsEvent/fetchData', async (config, thunkAPI: any) => {
  const controller = new DataController('NewsEvent');
  try {
    const res = await controller.aggregateList({
      page: config?.page ?? 1,
      size: 2,
      sortby: [{prop: 'DateCreated', direction: 'DESC'}],
    });
    if (res.code === 200 && res.data.length > 0) {
      const listData = await getImage({items: res.data});
      return {
        data: listData,
        totalCount: res.totalCount,
      };
    }
  } catch (err: any) {
    const errorMessage = err.message || 'An unknown error occurred';
    showSnackbar({message: errorMessage, status: ComponentStatus.ERROR});
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

const loadMoreNewsEvent = createAsyncThunk<
  NewsEvent[],
  {page?: number; status?: number} | undefined,
  {state: RootState}
>('newsEvent/loadMoreNewsEvent', async (config, thunkAPI) => {
  console.log('start load more');
  const controller = new DataController('NewsEvent');
  try {
    const res = await controller.aggregateList({
      page: config?.page ?? 1,
      size: 2,
      sortby: [{prop: 'DateCreated', direction: 'DESC'}],
    });
    if (res.code === 200 && res.data.length > 0) {
      const listData = await getImage({items: res.data});
      return listData;
    }
    return [];
  } catch (err: any) {
    const errorMessage = err.message || 'An unknown error occurred';
    showSnackbar({message: errorMessage, status: ComponentStatus.ERROR});
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

export {fetchNewsEvents, loadMoreNewsEvent};
