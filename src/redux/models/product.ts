interface Product {
  Id: string;
  ViolentNote: string;
  NumberUserReview: number | null;
  InStock: number | null;
  Like: number | null;
  Price: number;
  Sold: number | null;
  Star: number | null;
  Img: string;
  Sort: number;
  Name: string;
  Description: string;
  Type: number;
  Status: number;
  ShopId: string;
  CategoryId: string;
  Discount: number | null;
  rating: number | null;
  soldCount: number | null;
  IsFavorite: boolean;
  IsHot: boolean | null;
  IsFreeShip: boolean | null;
}
export type {Product};
