import React, {useState} from 'react';
import {
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  View,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';

import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {ColorThemes} from '../../../assets/skin/colors';

interface Props {
  data: any[];
  onChangeTab: (tabId: string) => void;
}

const ScrollableTabs = ({data, onChangeTab}: Props) => {
  const [activeTabId, setActiveTabId] = useState(data[0].id);

  const handleTabPress = (tabId: string) => {
    if (tabId === activeTabId) return;
    setActiveTabId(tabId);
    onChangeTab(tabId);
  };

  const renderTabItem = ({item}: {item: any}) => {
    const isActive = item.id === activeTabId;

    const getColor = (item: any) => {
      if (item.color) return item.color;
      if (isActive) return ColorThemes.light.primary_main_color;
      return '#333';
    };

    const TabContent = () => (
      <View style={styles.tabItem}>
        <FontAwesomeIcon
          icon={item.icon}
          size={16}
          color={getColor(item)}
          style={styles.tabIcon}
        />
        <Text
          style={[
            styles.tabLabel,
            isActive && {color: ColorThemes.light.primary_main_color},
          ]}>
          {item.label}
        </Text>
      </View>
    );

    return (
      <TouchableOpacity
        style={{
          borderRadius: 10,
          paddingHorizontal: 10,
        }}
        onPress={() => handleTabPress(item.id)}>
        {isActive ? (
          <LinearGradient
            colors={['#90C8FB', '#8DC4F7E5', '#B6F5FE']}
            start={{x: 0, y: 0.5}}
            end={{x: 1, y: 0.5}}
            style={{borderRadius: 10}}>
            <TabContent />
          </LinearGradient>
        ) : (
          <TabContent />
        )}
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView>
      <FlatList
        data={data}
        renderItem={renderTabItem}
        keyExtractor={item => item.id}
        horizontal={true} // Quan trọng: để list cuộn ngang
        showsHorizontalScrollIndicator={false} // Ẩn thanh cuộn ngang cho giao diện sạch hơn
        style={styles.listContentContainer}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  listContentContainer: {
    paddingHorizontal: 10,
    paddingVertical: 10,
  },
  tabItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 10,
    borderRadius: 10,
  },
  tabIcon: {
    marginRight: 8,
  },
  tabLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333', // Màu chữ mặc định
  },
  activeTabLabel: {
    color: '#007bff', // Màu chữ khi được chọn
  },
});

export default ScrollableTabs;
