import React, {useState, useEffect} from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Pressable,
} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {SORT_OPTIONS} from './PopupFilterProductComponent/constants';
import {
  AppliedFilters,
  ActiveFilters,
  SelectableItem,
} from './PopupFilterProductComponent/types';
import CustomPicker from './PopupFilterProductComponent/CustomPicker';
import FilterTag from './PopupFilterProductComponent/FilterTag';
import PriceSlider from './PopupFilterProductComponent/PriceSlider';
import PrimaryButton from './PopupFilterProductComponent/PrimaryButton';
import Section from './PopupFilterProductComponent/Section';
import {ColorThemes} from '../../../assets/skin/colors';
import {RootState} from '../../../redux/store/store';
import {fetchCategories} from '../../../redux/actions/categoryAction';
import {fetchBrands} from '../../../redux/actions/brandAction';
import {Category} from '../../../redux/models/category';
import {Brand} from '../../../redux/models/brand';

interface PopupFilterProductProps {
  visible: boolean;
  onClose: () => void;
  onApply: (filters: AppliedFilters) => void;
}

const mapToSelectableItem = (items: (Category | Brand)[]): SelectableItem[] => {
  return items.map(item => ({id: item.Id, name: item.Name}));
};

export default function PopupFilterProduct({
  visible,
  onClose,
  onApply,
}: PopupFilterProductProps) {
  const dispatch = useDispatch<any>();
  const {data: categories} = useSelector((state: RootState) => state.category);

  const {data: brands} = useSelector((state: RootState) => state.brand);

  const [category, setCategory] = useState<string | null>(null);
  const [brand, setBrand] = useState<string | null>(null);
  const [sortOption, setSortOption] = useState(SORT_OPTIONS[0].id);
  const [activeFilters, setActiveFilters] = useState<ActiveFilters>({
    hot: true,
    freeShip: true,
    new: false,
  });
  const [price, setPrice] = useState(200000);

  useEffect(() => {
    if (visible) {
      dispatch(fetchCategories());
      dispatch(fetchBrands());
    }
  }, [visible, dispatch]);

  const handleApplyFilters = () => {
    const data = {
      categoryId: category,
      brandId: brand,
      sortOption,
      activeFilters,
      maxPrice: price,
    };

    onApply(data);
    onClose();
  };

  const selectableCategories = mapToSelectableItem(categories);
  const selectableBrands = mapToSelectableItem(brands);

  const categoryLabel =
    selectableCategories.find(c => c.id === category)?.name || 'Chọn danh mục';
  const brandLabel =
    selectableBrands.find(b => b.id === brand)?.name || 'Chọn thương hiệu';
  const sortOptionLabel =
    SORT_OPTIONS.find(s => s.id === sortOption)?.name || '';

  return (
    <>
      <Modal
        animationType="slide"
        transparent={true}
        visible={visible}
        onRequestClose={onClose}>
        <Pressable style={styles.modalBackdrop} onPress={onClose} />
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Bộ lọc & Sắp xếp</Text>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
          </View>

          <Section title="Danh mục">
            <CustomPicker
              label={categoryLabel}
              data={selectableCategories}
              selectedValue={category}
              onSelect={setCategory}
              modalTitle="Chọn danh mục"
            />
          </Section>

          <Section title="Thương hiệu">
            <CustomPicker
              label={brandLabel}
              data={selectableBrands}
              selectedValue={brand}
              onSelect={setBrand}
              modalTitle="Chọn thương hiệu"
            />
          </Section>

          <Section title="Sắp xếp">
            <CustomPicker
              label={sortOptionLabel}
              data={SORT_OPTIONS}
              selectedValue={sortOption}
              onSelect={setSortOption}
              modalTitle="Sắp xếp theo"
            />
          </Section>

          <Section title="Bộ lọc">
            <View style={styles.filterTagGroup}>
              <FilterTag
                type="hot"
                label="HOT"
                isActive={activeFilters.hot}
                onPress={() =>
                  setActiveFilters(prev => ({...prev, hot: !prev.hot}))
                }
                backgroundColor={ColorThemes.light.secondary6_darker_color}
              />
              <FilterTag
                type="freeShip"
                label="Free ship"
                isActive={activeFilters.freeShip}
                onPress={() =>
                  setActiveFilters(prev => ({
                    ...prev,
                    freeShip: !prev.freeShip,
                  }))
                }
                backgroundColor={ColorThemes.light.secondary2_main_color}
              />
            </View>
          </Section>

          <Section title="Giá">
            <PriceSlider value={price} onValueChange={setPrice} />
          </Section>

          <PrimaryButton title="Áp dụng" onPress={handleApplyFilters} />
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  modalBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.4)',
  },
  modalContainer: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
    backgroundColor: 'white',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    padding: 20,
    paddingBottom: 30,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    position: 'relative',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  closeButton: {
    position: 'absolute',
    right: 0,
    top: -5,
    backgroundColor: '#F0F0F0',
    borderRadius: 15,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 16,
    color: '#888',
    fontWeight: 'bold',
  },
  filterTagGroup: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
});
