import React, {useState, useEffect, useRef} from 'react';
import {View, Text, StyleSheet} from 'react-native';
import Slider from '@react-native-community/slider';

interface PriceSliderProps {
  value: number;
  onValueChange: (value: number) => void;
}

const PriceSlider = ({value, onValueChange}: PriceSliderProps) => {
  const [internalValue, setInternalValue] = useState(value);
  const debounceTimeout = useRef<NodeJS.Timeout | null>(null);

  // Sync internal state if the external value prop changes
  useEffect(() => {
    setInternalValue(value);
  }, [value]);

  const handleValueChange = (newValue: number) => {
    setInternalValue(newValue);

    if (debounceTimeout.current) {
      clearTimeout(debounceTimeout.current);
    }

    debounceTimeout.current = setTimeout(() => {
      onValueChange(newValue);
    }, 500);
  };

  // Cleanup the timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeout.current) {
        clearTimeout(debounceTimeout.current);
      }
    };
  }, []);

  return (
    <View>
      <Text style={styles.priceRangeText}>
        {`10.000đ — ${internalValue.toLocaleString()}đ`}
      </Text>
      <Slider
        style={styles.slider}
        minimumValue={10000}
        maximumValue={1000000}
        step={10000}
        minimumTrackTintColor="#0052FF"
        maximumTrackTintColor="#E0E0E0"
        thumbTintColor="#0052FF"
        value={internalValue}
        onValueChange={handleValueChange}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  priceRangeText: {
    textAlign: 'center',
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 5,
  },
  slider: {
    width: '100%',
    height: 40,
  },
});

export default PriceSlider;
