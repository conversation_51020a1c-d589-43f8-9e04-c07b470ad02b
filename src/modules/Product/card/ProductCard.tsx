import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import {Winicon, showSnackbar, ComponentStatus} from 'wini-mobile-components';
import {useDispatch} from 'react-redux';
import {AppDispatch} from '../../../redux/store/store';
import ConfigAPI from '../../../Config/ConfigAPI';
import {Ultis} from '../../../utils/Utils';
import {ColorThemes} from '../../../assets/skin/colors';
import {CartActions} from '../../../redux/reducers/CartReducer';
import {TypoSkin} from '../../../assets/skin/typography';
import {Product} from '../../../redux/models/product';
import {updateFavoriteProduct} from '../../../redux/actions/productAction';

const {width} = Dimensions.get('window');

// Kích thước mặc định của card sản phẩm
const DEFAULT_ITEM_WIDTH = width * 0.45;
const DEFAULT_ITEM_HEIGHT = DEFAULT_ITEM_WIDTH * 2;

interface ProductCardProps {
  item: Product;
  onPress?: (item: Product) => void;
  onAddToCart?: (item: Product) => void;
  onFavoritePress?: (item: Product) => void;
  width?: number;
  height?: number;
}

const ProductCard: React.FC<ProductCardProps> = ({
  item,
  onPress,
  onAddToCart,
  onFavoritePress,
  width: cardWidth = DEFAULT_ITEM_WIDTH,
  height: cardHeight = DEFAULT_ITEM_HEIGHT,
}) => {
  const dispatch: AppDispatch = useDispatch();

  const onPressIconCart = () => {
    // Nếu có hàm xử lý từ component cha
    if (onAddToCart) {
      onAddToCart(item);
    } else {
      // Thêm sản phẩm vào giỏ hàng
      CartActions.addItemToCart(item, 1)(dispatch);

      // Hiển thị thông báo
      showSnackbar({
        message: 'Đã thêm sản phẩm vào giỏ hàng',
        status: ComponentStatus.SUCCSESS,
      });
    }
  };

  const onPressIconHeart = () => {
    if (item.IsFavorite) {
      dispatch(updateFavoriteProduct({...item, IsFavorite: false}));
    } else {
      dispatch(updateFavoriteProduct({...item, IsFavorite: true}));
    }
  };

  return (
    <TouchableOpacity
      style={[styles.itemContainer, {width: cardWidth, height: cardHeight}]}
      onPress={() => onPress && onPress(item)}
      activeOpacity={0.8}>
      {/* Ảnh sản phẩm */}
      <View style={styles.imageContainer}>
        <FastImage
          source={{uri: ConfigAPI.urlImg + item.Img}}
          style={styles.image}
          resizeMode={FastImage.resizeMode.cover}
        />

        {/* Badge giảm giá */}
        {item.Discount && item.Discount > 0 ? (
          <View style={styles.discountBadge}>
            <Text style={styles.discountText}>-{item.Discount}%</Text>
          </View>
        ) : null}

        {/* Logo thương hiệu */}
        <View style={styles.brandLogo}>
          {/* <Text style={styles.brandText}>INTAR</Text>
          <Text style={styles.brandSubText}>Pills Power</Text> */}
        </View>
      </View>

      {/* Thông tin sản phẩm */}
      <View style={styles.infoContainer}>
        <Text style={styles.title} numberOfLines={2}>
          {item.Name || ''}
        </Text>

        <View>
          <View style={styles.priceContainer}>
            <Text style={styles.price}>{Ultis.money(item.Price || 0)} đ</Text>
            {item.Discount && item.Discount > 0 ? (
              <Text style={styles.originalPrice}>
                {Ultis.money(item.Price / (1 - item.Discount / 100))} đ
              </Text>
            ) : null}
          </View>
          <View style={styles.bottomRow}>
            <View style={styles.ratingContainer}>
              <Winicon
                src="fill/user interface/star"
                size={14}
                color={ColorThemes.light.warning_main_color}
              />
              {item.rating ? (
                <Text style={styles.ratingText}>{item.rating}</Text>
              ) : null}
              <Text style={styles.soldText}>
                | Đã bán {item.soldCount ?? 0}
              </Text>
            </View>
            <View style={styles.actionButtons}>
              <TouchableOpacity
                style={styles.cartButton}
                onPress={onPressIconHeart}
                activeOpacity={0.7}>
                {item.IsFavorite ? (
                  <Winicon src="color/emoticons/heart" size={15} />
                ) : (
                  <Winicon
                    src="outline/emoticons/heart"
                    size={15}
                    color="#666"
                  />
                )}
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.cartButton, {marginLeft: 6}]}
                onPress={onPressIconCart}
                activeOpacity={0.7}>
                <Winicon src="outline/shopping/cart" size={15} color="#666" />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  itemContainer: {
    borderRadius: 10,
    backgroundColor: '#fff',
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#F0F0F0',
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  imageContainer: {
    width: '100%',
    height: '60%',
    position: 'relative',
    backgroundColor: '#fff', // Màu nền hồng nhạt cho ảnh
  },
  image: {
    width: '95%',
    height: '95%',
    alignSelf: 'center',
    borderRadius: 8,
    marginTop: 8,
    backgroundColor: '#fff',
  },
  discountBadge: {
    position: 'absolute',
    top: 8,
    left: 8,
    backgroundColor: '#FFA500',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  discountText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 12,
  },
  brandLogo: {
    position: 'absolute',
    top: 4,
    right: 4,
    padding: 4,
    borderRadius: 4,
    alignItems: 'center',
  },
  brandText: {
    color: '#4169E1',
    fontWeight: 'bold',
    fontSize: 12,
  },
  brandSubText: {
    color: '#4169E1',
    fontSize: 8,
  },
  infoContainer: {
    padding: 8,
    flex: 1,
    justifyContent: 'space-between',
  },
  title: {
    ...TypoSkin.title5,
    color: ColorThemes.light.neutral_text_title_color,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  price: {
    ...TypoSkin.title5,
    color: ColorThemes.light.neutral_text_title_color,
  },
  originalPrice: {
    fontSize: 12,
    color: '#999',
    textDecorationLine: 'line-through',
    marginLeft: 8,
  },
  bottomRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 4,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  ratingText: {
    ...TypoSkin.subtitle4,
    color: ColorThemes.light.warning_main_color,
    marginLeft: 4,
  },
  soldText: {
    ...TypoSkin.subtitle4,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginLeft: 4,
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  favoriteButton: {
    // marginRight: 8,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartButton: {
    width: 26,
    height: 26,
    backgroundColor: ColorThemes.light.neutral_bolder_background_color,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ProductCard;
