import {
  ActivityIndicator,
  Dimensions,
  FlatList,
  RefreshControl,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {AppSvg, FBottomSheet, FDialog, TextField} from 'wini-mobile-components';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {ColorThemes} from '../../../assets/skin/colors';
import {useForm} from 'react-hook-form';
import {RootScreen} from '../../../router/router';
import {useNavigation} from '@react-navigation/native';
import {useTranslation} from 'react-i18next';

import {ProductDA} from '../productDA';
import ProductCard from '../card/ProductCard';
import {CartActions} from '../../../redux/reducers/CartReducer';
import {AppDispatch} from '../../../redux/store/store';
import {useDispatch} from 'react-redux';
import HeaderLogo from '../../../Screen/Layout/headers/HeaderLogo';
import iconSvg from '../../../svg/icon';
import {
  getDataToAsyncStorage,
  saveDataToAsyncStorage,
} from '../../../utils/AsyncStorage';

const {width} = Dimensions.get('window');

const ITEM_WIDTH = width * 0.45;
const ITEM_HEIGHT = ITEM_WIDTH * 2; // Tăng chiều cao một chút

export const SearchIndex = () => {
  const [searchValue, setSearchValue] = useState('');
  const bottomSheetRef = useRef<any>(null);
  const dialogRef = useRef<any>(null);
  const filterMethods = useForm({shouldFocusError: false});

  const [data, setData] = useState<Array<any>>([]);
  const [isRefresh, setRefresh] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const {t} = useTranslation();
  const navigation = useNavigation<any>();

  const [page, setPage] = useState(1);
  const size = 10;

  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMoreData, setHasMoreData] = useState(false);
  const productDA = new ProductDA();
  const dispatch: AppDispatch = useDispatch();

  const [searchHistory, setSearchHistory] = useState<any>([]);
  const [recentProducts, setRecentProducts] = useState<any>([]);

  const SEARCH_HISTORY_KEY = 'search_history';
  const RECENT_PRODUCTS_KEY = 'recent_products';
  const MAX_HISTORY_ITEMS = 5;
  const MAX_RECENT_PRODUCTS = 6;

  // Load search history and recent products when component mounts
  useEffect(() => {
    loadSearchHistory();
    loadRecentProducts();
  }, []);

  const loadSearchHistory = async () => {
    try {
      const history = await getDataToAsyncStorage(SEARCH_HISTORY_KEY);
      if (history !== null) {
        setSearchHistory(JSON.parse(history));
      }
    } catch (error) {
      console.error('Error loading search history:', error);
    }
  };

  const loadRecentProducts = async () => {
    try {
      const products = await getDataToAsyncStorage(RECENT_PRODUCTS_KEY);
      if (products !== null) {
        setRecentProducts(JSON.parse(products));
      }
    } catch (error) {
      console.error('Error loading recent products:', error);
    }
  };

  const saveSearchHistory = async (newHistory: any) => {
    try {
      await saveDataToAsyncStorage(
        SEARCH_HISTORY_KEY,
        JSON.stringify(newHistory),
      );
    } catch (error) {
      console.error('Error saving search history:', error);
    }
  };

  const saveRecentProducts = async (newProducts: any) => {
    try {
      await saveDataToAsyncStorage(
        RECENT_PRODUCTS_KEY,
        JSON.stringify(newProducts),
      );
    } catch (error) {
      console.error('Error saving recent products:', error);
    }
  };

  const addToSearchHistory = async (searchTerm: string) => {
    const trimmedTerm = searchTerm.trim();

    if (trimmedTerm && !searchHistory.includes(trimmedTerm)) {
      let newHistory = [trimmedTerm, ...searchHistory];

      // Limit to MAX_HISTORY_ITEMS
      if (newHistory.length > MAX_HISTORY_ITEMS) {
        newHistory = newHistory.slice(0, MAX_HISTORY_ITEMS);
      }

      setSearchHistory(newHistory);
      await saveSearchHistory(newHistory);
    }
  };

  const addToRecentProducts = async (products: any[]) => {
    if (products && products.length > 0) {
      // Take first few products from search results
      const productsToAdd = products.slice(0, 3);

      // Remove duplicates and add new products to the beginning
      let newRecentProducts = [...productsToAdd];

      // Add existing products that are not duplicates
      recentProducts.forEach((existingProduct: any) => {
        const isDuplicate = newRecentProducts.some(
          (newProduct: any) => newProduct.Id === existingProduct.Id,
        );
        if (!isDuplicate && newRecentProducts.length < MAX_RECENT_PRODUCTS) {
          newRecentProducts.push(existingProduct);
        }
      });

      // Limit to MAX_RECENT_PRODUCTS
      if (newRecentProducts.length > MAX_RECENT_PRODUCTS) {
        newRecentProducts = newRecentProducts.slice(0, MAX_RECENT_PRODUCTS);
      }

      setRecentProducts(newRecentProducts);
      await saveRecentProducts(newRecentProducts);
    }
  };

  // const removeFromSearchHistory = async (termToRemove: any) => {
  //   const newHistory = searchHistory.filter((term: any) => term !== termToRemove);
  //   setSearchHistory(newHistory);
  //   await saveSearchHistory(newHistory);
  // };

  // const clearSearchHistory = async () => {
  //   setSearchHistory([]);
  //   await removeDataToAsyncStorage(SEARCH_HISTORY_KEY);
  // };

  // const handleSearch = () => {
  //   if (searchText.trim()) {
  //     addToSearchHistory(searchText);
  //     // Perform search logic here
  //     console.log('Searching for:', searchText);
  //   }
  // };

  const handleHistoryItemPress = async (term: string) => {
    setSearchValue(term);
    // Trigger search immediately
    await searchProducts(term);
  };

  const searchProducts = useCallback(
    async (
      searchText: string,
      pageNumber: number = 1,
      isRefresh: boolean = false,
    ) => {
      if (!searchText.trim()) {
        setData([]);
        return;
      }

      try {
        if (pageNumber === 1) {
          isRefresh ? setRefresh(true) : setIsLoading(true);
        } else {
          setIsLoadingMore(true);
        }

        // Create search query for product name
        const query = `@Name: (*${searchText.trim()}*)`;

        const result = await productDA.getAllList(pageNumber, 20, query);

        if (result && result.code === 200) {
          const newProducts = result.data || [];

          if (pageNumber === 1) {
            setData(newProducts);
            // Add to search history and recent products only when search is completed successfully
            if (newProducts.length > 0) {
              await addToSearchHistory(searchText);
              await addToRecentProducts(newProducts);
            }
          } else {
            setData(prev => [...prev, ...newProducts]);
          }

          // Check if there's more data
          setHasMoreData(newProducts.length === 20);
        } else {
          if (pageNumber === 1) {
            setData([]);
          }
        }
        setRefresh(false);
      } catch (err) {
        console.error('Error searching products:', err);
        if (pageNumber === 1) {
          setData([]);
        }
      } finally {
        setIsLoading(false);
        setRefresh(false);
        setIsLoadingMore(false);
      }
    },
    [productDA],
  );

  const onRefresh = async () => {
    setPage(1);
    await searchProducts(searchValue);
  };

  return (
    <View
      style={{
        width: '100%',
        height: Dimensions.get('window').height,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <FBottomSheet ref={bottomSheetRef} />
      <FDialog ref={dialogRef} />
      <HeaderLogo props={{onBack: () => navigation.goBack()}} />
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          flex: 1,
          paddingHorizontal: 16,
          gap: 8,
          marginVertical: 32,
        }}>
        <TextField
          style={{
            paddingHorizontal: 16,
            width: '90%',
            height: 36,
          }}
          onChange={async (vl: string) => {
            setSearchValue(vl.trim());
            await searchProducts(vl.trim());
          }}
          value={searchValue}
          placeholder="Bạn muốn tìm gì?"
        />
        <AppSvg
          SvgSrc={iconSvg.filter}
          color="#000"
          size={25}
          style={{marginRight: 12}}
        />
      </View>
      <FlatList
        data={data}
        refreshControl={
          <RefreshControl
            refreshing={isRefresh}
            onRefresh={() => {
              onRefresh();
            }}
          />
        }
        contentContainerStyle={{
          gap: 16,
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        }}
        renderItem={({item, index}) => {
          return (
            <ProductCard
              key={index}
              onPress={() => {
                navigation.push(RootScreen.ProductDetail, {id: item.Id});
              }}
              item={item}
              onAddToCart={() => {
                dispatch(CartActions.addItemToCart(item, 1));
              }}
              onFavoritePress={() => {}}
              width={ITEM_WIDTH}
              height={ITEM_HEIGHT}
            />
          );
        }}
        style={{
          width: '100%',
          height: '100%',
          paddingTop: 16,
          paddingHorizontal: 16,
        }}
        numColumns={2}
        keyExtractor={item => item.Id?.toString()}
        ListEmptyComponent={() => {
          if (isLoading) {
            return (
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  flexDirection: 'row',
                }}>
                <ActivityIndicator
                  color={ColorThemes.light.Primary_Color_Main}
                />
              </View>
            );
          }
          return (
            <EmptySearchWithHistory
              histories={searchHistory}
              recentProducts={recentProducts}
              onHistoryPress={handleHistoryItemPress}
              onProductPress={(product: any) => {
                navigation.push(RootScreen.ProductDetail, {id: product.Id});
              }}
              onAddToCart={(product: any) => {
                dispatch(CartActions.addItemToCart(product, 1));
              }}
            />
          );
        }}
      />
    </View>
  );
};

interface EmptySearchWithHistoryProps {
  histories: string[];
  recentProducts: any[];
  onHistoryPress: (term: string) => void;
  onProductPress: (product: any) => void;
  onAddToCart: (product: any) => void;
}

const EmptySearchWithHistory = ({
  histories,
  recentProducts,
  onHistoryPress,
  onProductPress,
}: EmptySearchWithHistoryProps) => {
  return (
    <View style={{height: '100%', width: '100%'}}>
      {/* History Section */}
      {histories && histories.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Lịch sử tìm kiếm</Text>
          <View style={styles.categoryContainer}>
            {histories.map((term: string, index: number) => (
              <TouchableOpacity
                key={index}
                style={styles.categoryButton}
                onPress={() => onHistoryPress(term)}>
                <Text style={styles.categoryText}>{term}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      )}

      {/* Recently Viewed Section */}
      {recentProducts && recentProducts.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Sản phẩm gần đây</Text>
          <View style={styles.productGrid}>
            {recentProducts.map((product: any) => {
              return (
                <ProductCard
                  key={product.Id}
                  onPress={() => onProductPress(product)}
                  onAddToCart={() => onAddToCart(product)}
                  item={product}
                  onFavoritePress={() => {}}
                  width={ITEM_WIDTH}
                  height={ITEM_HEIGHT}
                />
              );
            })}
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#4A90E2',
    paddingTop: 10,
    paddingBottom: 15,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
  },
  menuIcon: {
    width: 24,
    height: 24,
    justifyContent: 'space-between',
  },
  menuLine: {
    width: 20,
    height: 2,
    backgroundColor: 'white',
  },
  logo: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    letterSpacing: 1,
  },
  headerIcons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    marginLeft: 15,
  },
  gameIcon: {
    width: 24,
    height: 24,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 12,
  },
  profileIcon: {
    width: 24,
    height: 24,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 12,
  },
  cartIcon: {
    width: 24,
    height: 24,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 12,
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartBadge: {
    position: 'absolute',
    top: -5,
    right: -5,
    backgroundColor: '#FF6B6B',
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
    paddingHorizontal: 4,
    paddingVertical: 1,
    borderRadius: 8,
    minWidth: 16,
    textAlign: 'center',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  searchContainer: {
    flexDirection: 'row',
    marginBottom: 20,
    alignItems: 'center',
  },
  searchInput: {
    flex: 1,
    backgroundColor: 'white',
    borderRadius: 25,
    paddingHorizontal: 20,
    paddingVertical: 12,
    fontSize: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  filterButton: {
    marginLeft: 10,
    backgroundColor: 'white',
    borderRadius: 25,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  filterIcon: {
    fontSize: 18,
    color: '#666',
  },
  section: {
    marginBottom: 25,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  categoryContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
  },
  categoryButton: {
    backgroundColor: '#E8E8E8',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 10,
    marginBottom: 10,
  },
  categoryText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  productGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  productCard: {
    width: '48%',
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 10,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  productImage: {
    width: '100%',
    height: 120,
    borderRadius: 8,
    marginBottom: 8,
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    backgroundColor: '#E0E0E0',
    borderRadius: 8,
  },
  productTitle: {
    fontSize: 12,
    color: '#333',
    marginBottom: 5,
    lineHeight: 16,
  },
  productPrice: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#4A90E2',
  },
});
