import React, {useEffect} from 'react';
import {ScrollView, StyleSheet} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useRoute} from '@react-navigation/native';
import {ColorThemes} from '../../../assets/skin/colors';

import CategoryHeader from '../../../Screen/Layout/headers/CategoryHeader';

import {categoryAction} from '../../../redux/actions/categoryAction';
import {useProductByCategoryHook} from '../../../redux/reducers/ProductByCategoryReducer';
import ListProduct from './ProductList/ListProduct';
import ListCategory from './ProductList/ListCategory';
import ScrollOption from './ProductList/ScrollOption';
import {useSelector} from 'react-redux';
import {RootState} from '../../../redux/store/store';

export default function ProductListByCategory() {
  const productByCategoryHook = useProductByCategoryHook();
  const {filter, newCategoryId} = useSelector(
    (state: RootState) => state.productByCategory,
  );
  const categoryId = useRoute<any>()?.params?.categoryId;

  useEffect(() => {
    productByCategoryHook.resetData();
    initData(categoryId);
  }, [categoryId]);

  useEffect(() => {
    if (newCategoryId) {
      categoryChange(newCategoryId);
    }
  }, [newCategoryId]);

  const initData = async (categoryId: string) => {
    const currentCategory = await categoryAction.findOne(categoryId);
    let parentCategory = null;
    let childrenCategory = [];
    if (currentCategory.ParentId) {
      parentCategory = await categoryAction.findOne(currentCategory.ParentId);
      childrenCategory = await categoryAction.find(
        `@ParentId:{${currentCategory.ParentId}}`,
      );
    } else {
      parentCategory = currentCategory;
      childrenCategory = await categoryAction.find(
        `@ParentId:{${currentCategory.Id}}`,
      );
    }

    childrenCategory.unshift({
      Id: 'all',
      Name: 'Tất cả',
      ParentId: currentCategory.ParentId,
    });

    productByCategoryHook.setData('parentCategory', parentCategory);
    productByCategoryHook.setData('childrenCategory', childrenCategory);
    productByCategoryHook.setData('currentCategory', currentCategory);
    productByCategoryHook.setData('filter', {
      ...filter,
      categoryId: categoryId,
    });
  };

  const categoryChange = async (categoryId: string) => {
    const currentCategory = await categoryAction.findOne(categoryId);
    let parentCategory = null;
    let childrenCategory = [];
    if (currentCategory.ParentId) {
      parentCategory = await categoryAction.findOne(currentCategory.ParentId);
      childrenCategory = await categoryAction.find(
        `@ParentId:{${currentCategory.ParentId}}`,
      );
    } else {
      parentCategory = currentCategory;
      childrenCategory = await categoryAction.find(
        `@ParentId:{${currentCategory.Id}}`,
      );
    }

    childrenCategory.unshift({
      Id: 'all',
      Name: 'Tất cả',
      ParentId: currentCategory.ParentId,
    });

    productByCategoryHook.setData('parentCategory', parentCategory);
    productByCategoryHook.setData('childrenCategory', childrenCategory);
    productByCategoryHook.setData('currentCategory', currentCategory);
  };

  return (
    <SafeAreaView edges={['bottom']} style={styles.container}>
      <CategoryHeader />
      <ScrollOption />
      <ListCategory />
      <ListProduct />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  productList: {
    padding: 16,
    gap: 16,
  },
  footerLoader: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 16,
  },
  footerText: {
    marginLeft: 8,
    fontSize: 14,
    color: ColorThemes.light.primary_main_color,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#FF6B6B',
    textAlign: 'center',
  },
});
