import React, {useEffect, useState, useRef, useCallback} from 'react';
import {
  SafeAreaView,
  View,
  FlatList,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import Header from './FavoriteProductComponent/Header';
import ProductItem from './FavoriteProductComponent/ProductItem';
import SearchBar from '../../components/shop/Search';
import {InforHeader} from '../../Screen/Layout/headers/inforHeader';
import {
  fetchFavoriteProducts,
  loadmoreFavoriteProducts,
} from '../../redux/actions/favoriteProductAction';
import {useDispatch, useSelector} from 'react-redux';
import {RootState} from '../../redux/store/store';
import LoadMoreButton from '../../components/LoadMoreButton';
import {ColorThemes} from '../../assets/skin/colors';

const FavouriteProduct = () => {
  const dispatch = useDispatch();
  const {data, loading, loadingMore, totalCount, size} = useSelector(
    (state: RootState) => state.favoriteProduct,
  );

  //state
  const [page, setPage] = useState(1);
  const [query, setQuery] = useState('');
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  //effect
  useEffect(() => {
    dispatch(fetchFavoriteProducts({size: size}) as any);

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  const onLoadMore = () => {
    setPage(page + 1);
    dispatch(
      loadmoreFavoriteProducts({
        page: page / size + 1,
        size: size,
        search: query,
      }) as any,
    );
  };

  const onSearch = useCallback(
    (text: string) => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }

      searchTimeoutRef.current = setTimeout(() => {
        setPage(1);
        setQuery(text);
        dispatch(fetchFavoriteProducts({search: text}) as any);
      }, 1000);
    },
    [dispatch],
  );

  return (
    <SafeAreaView style={styles.screen}>
      <InforHeader title={'Sản phẩm yêu thích'} />
      <SearchBar setDataSearch={onSearch} />
      {loading ? (
        <View style={styles.center}>
          <ActivityIndicator
            size="large"
            color={ColorThemes.light.primary_main_color}
          />
        </View>
      ) : (
        <View style={{flex: 1}}>
          <View style={styles.container}>
            <Header title="Sản phẩm yêu thích" productCount={data.length} />
            <FlatList
              data={data}
              renderItem={({item}) => <ProductItem item={item} />}
              keyExtractor={item => item.Id}
              ItemSeparatorComponent={() => <View style={styles.separator} />}
              showsVerticalScrollIndicator={false}
              ListFooterComponent={
                data.length < totalCount ? (
                  <LoadMoreButton onPress={onLoadMore} loading={loadingMore} />
                ) : null
              }
            />
          </View>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  screen: {
    flex: 1,
    backgroundColor: '#F8F8F8',
  },
  container: {
    flex: 1,
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  separator: {
    height: 16,
  },
  center: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default FavouriteProduct;
