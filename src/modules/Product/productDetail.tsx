import {
  Text,
  useWindowDimensions,
  View,
  RefreshControl,
  StyleSheet,
  ScrollView,
  Dimensions,
  TouchableOpacity,
  FlatList,
  Image,
} from 'react-native';
import {useEffect, useRef, useState, useCallback, useMemo} from 'react';
import {
  AppSvg,
  Winicon,
  showSnackbar,
  ComponentStatus,
  Rating,
} from 'wini-mobile-components';
import {useNavigation, useRoute} from '@react-navigation/native';
import {ProductDA} from './productDA';
import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';
import RenderHTML from 'react-native-render-html';
import {SwiperFlatList} from 'react-native-swiper-flatlist';
import FastImage from 'react-native-fast-image';
import {RootScreen} from '../../router/router';
import {Ultis} from '../../utils/Utils';
import iconSvg from '../../svg/icon';
import {useDispatch} from 'react-redux';
import {CartActions} from '../../redux/reducers/CartReducer';
import CartIcon from '../../components/CartIcon';
import {AppDispatch} from '../../redux/store/store';
import {SafeAreaView} from 'react-native-safe-area-context';
import ConfigAPI from '../../Config/ConfigAPI';
import {CustomerDA} from '../customer/da';

export default function ProductDetail() {
  const [like, setLike] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [_loading, setLoading] = useState(true);
  const route = useRoute<any>();
  const {id} = route.params;
  const [data, setData] = useState<any>();
  const [shop, setShop] = useState<any>();
  const navigation = useNavigation<any>();
  const scrollviewRef = useRef<any>(null);
  const {width} = useWindowDimensions();
  const dispatch: AppDispatch = useDispatch();
  const customerDA = new CustomerDA();
  // Initialize productDA with useMemo to avoid recreating on every render
  const productDA = useMemo(() => new ProductDA(), []);

  // Image carousel state
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const swiperRef = useRef<SwiperFlatList>(null);

  // Mock product images for demo (will be replaced with actual data)
  const [productImages, setProductImages] = useState([]);

  const getData = useCallback(async () => {
    if (!id) {
      setLoading(false);
      setRefreshing(false);
      return;
    }
    const result = await productDA.getProductDetail(id);
    if (result) {
      //lấy avatar của shop dựa vào customerId
      const customer = await customerDA.getCustomerItem(
        result.Shop[0].CustomerId,
      );
      setShop({...result.Shop[0], Img: customer?.AvatarUrl});
      if (result.data[0]?.ListImg || result.data[0].Img) {
        var listImg = result.data[0]?.ListImg
          ? result.data[0]?.ListImg.split(',')
          : [result.data[0].Img];
        listImg = listImg.filter((item: any) => item !== '');
        listImg = listImg.map((item: any) => `${ConfigAPI.urlImg}${item}`);
        setProductImages(listImg);
      }
      setData(result.data[0]);
      setLoading(false);
      setRefreshing(false);
    } else {
      setLoading(false);
      setRefreshing(false);
    }
  }, [id]);

  useEffect(() => {
    getData();
  }, [getData]);

  const onRefresh = () => {
    setRefreshing(true);
    getData();
  };

  return (
    <SafeAreaView edges={['bottom']} style={styles.container}>
      {/* Header with back button, cart and menu */}
      <View style={styles.header}>
        <TouchableOpacity
          style={{
            paddingRight: 16,
            paddingVertical: 8,
            alignItems: 'center',
            zIndex: 10,
          }}
          activeOpacity={0.7}
          hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
          onPress={event => {
            navigation.goBack();
          }}>
          <View
            style={{
              gap: 4,
              flexDirection: 'row',
              width: '100%',
              alignItems: 'center',
              borderRadius: 20,
              backgroundColor:
                ColorThemes.light.neutral_absolute_background_color,
              padding: 6,
            }}>
            <Winicon
              src="outline/arrows/left-arrow"
              size={20}
              color={ColorThemes.light.neutral_text_title_color}
            />
          </View>
        </TouchableOpacity>
        <View style={styles.headerRight}>
          <TouchableOpacity
            style={styles.cartButton}
            onPress={() => navigation.navigate(RootScreen.CartPage)}>
            <View
              style={{
                gap: 4,
                flexDirection: 'row',
                width: '100%',
                alignItems: 'center',
                borderRadius: 20,
                backgroundColor:
                  ColorThemes.light.neutral_absolute_background_color,
                padding: 4,
              }}>
              <CartIcon color="#1C33FF" size={20} isHome={false} />
            </View>
          </TouchableOpacity>
          <TouchableOpacity>
            <View
              style={{
                gap: 4,
                flexDirection: 'row',
                width: '100%',
                alignItems: 'center',
                borderRadius: 20,
                backgroundColor:
                  ColorThemes.light.neutral_absolute_background_color,
                padding: 8,
              }}>
              <Winicon
                src="fill/layout/dots-vertical"
                size={20}
                color="#1C33FF"
              />
            </View>
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView
        ref={scrollviewRef}
        nestedScrollEnabled
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[ColorThemes.light.primary_main_color]}
            tintColor={ColorThemes.light.primary_main_color}
          />
        }
        style={styles.scrollView}>
        {/* Product Image Carousel */}
        <View style={styles.imageContainer}>
          <SwiperFlatList
            ref={swiperRef}
            autoplay
            autoplayDelay={5}
            autoplayLoop
            showPagination={false}
            data={productImages}
            onChangeIndex={({index}) => {
              setCurrentImageIndex(index);
            }}
            renderItem={({item}) => (
              <FastImage
                key={item}
                source={{uri: item}}
                style={styles.productImage}
                resizeMode={FastImage.resizeMode.cover}
              />
            )}
          />
          <View style={styles.imageCounter}>
            <Text style={styles.imageCounterText}>
              {currentImageIndex + 1}/{productImages.length}
            </Text>
          </View>
        </View>

        {/* Thumbnail Gallery */}
        {/* <View style={styles.thumbnailContainer}>
          <Text style={styles.variantsTitle}>
            0{productImages.length} phân loại có sẵn
          </Text>
          <FlatList
            horizontal
            data={productImages}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.thumbnailList}
            renderItem={({item, index}) => (
              <TouchableOpacity
                style={[
                  styles.thumbnailItem,
                  currentImageIndex === index && styles.selectedThumbnail,
                ]}
                onPress={() => {
                  setCurrentImageIndex(index);
                  swiperRef.current?.scrollToIndex({index});
                }}>
                <FastImage
                  key={item}
                  source={{
                    uri: data?.Img ? ConfigAPI.urlImg + data?.Img : item,
                  }}
                  style={styles.thumbnailImage}
                  resizeMode={FastImage.resizeMode.cover}
                />
              </TouchableOpacity>
            )}
            keyExtractor={(_, index) => index.toString()}
          />
        </View> */}

        {/* Price and Actions */}
        <View style={styles.priceContainer}>
          <Text style={styles.priceText}>{Ultis.money(data?.Price)} đ</Text>
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={styles.favoriteButton}
              onPress={() => setLike(!like)}>
              <Winicon
                src={
                  like
                    ? 'fill/user interface/heart'
                    : 'outline/user interface/heart'
                }
                size={20}
                color={like ? '#FF0000' : '#000'}
              />
            </TouchableOpacity>
            <TouchableOpacity style={styles.shareButton}>
              <Winicon src="fill/user interface/share" size={20} color="#000" />
            </TouchableOpacity>
          </View>
        </View>

        {/* Product Title */}
        <Text style={styles.productTitle}>{data?.Name || ''}</Text>

        {/* Product Details */}
        <View style={styles.detailsContainer}>
          <View style={styles.detailsHorizontalContainer}>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Hãng: Gucci</Text>
            </View>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Đã bán: 100</Text>
            </View>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Còn: 900</Text>
            </View>
          </View>
        </View>

        {/* Shipping */}
        <View style={styles.shippingContainer}>
          <AppSvg SvgSrc={iconSvg.delivery} size={20} />
          <Text style={styles.shippingDistance}>3.0km</Text>
          <Text style={styles.shippingFree}>Free</Text>
        </View>

        {/* Rating */}
        <View style={styles.ratingContainer}>
          <Rating value={4} size={20} />
          <Text style={styles.ratingText}>4/5 (20)</Text>
          <View style={{flex: 1}} />
          <TouchableOpacity>
            <Text style={styles.viewAllText}>Xem tất cả</Text>
          </TouchableOpacity>
        </View>

        {/* Seller Info */}
        <View style={styles.sellerContainer}>
          <Image
            source={{
              uri: data?.Img
                ? ConfigAPI.urlImg + data?.Img
                : 'https://placehold.co/48',
            }}
            style={styles.sellerAvatar}
          />
          <View style={styles.sellerInfo}>
            <Text style={styles.sellerName}>{shop?.Name ?? ''}</Text>
            <View style={styles.sellerMeta}>
              <View style={styles.sellerBadge}>
                <AppSvg SvgSrc={iconSvg.ruby} size={14} />
                <Text style={styles.sellerBadgeText}>Ruby</Text>
              </View>
              <Text style={styles.sellerContact}>{shop?.Mobile ?? ''}</Text>
            </View>
          </View>
        </View>

        {/* Product Description */}
        {data?.Content && (
          <View style={styles.descriptionContainer}>
            <Text style={{...TypoSkin.semibold3, marginBottom: 12}}>
              Chi tiết sản phẩm
            </Text>
            <RenderHTML
              contentWidth={width}
              source={{html: data?.Content}}
              tagsStyles={{
                body: {
                  color: '#313135',
                  fontSize: 14,
                  lineHeight: 20,
                  fontFamily: 'Inter',
                },
              }}
            />
          </View>
        )}
      </ScrollView>

      {/* Bottom Actions */}
      <View style={styles.bottomActions}>
        <TouchableOpacity style={styles.chatButton}>
          <View style={styles.actionButtonContent}>
            <Winicon src="fill/user interface/f-chat" size={24} color="#fff" />
            <Text style={styles.actionButtonText}>Chat ngay</Text>
          </View>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.cartAddButton}
          onPress={() => {
            dispatch(CartActions.addItemToCart(data, 1));
          }}>
          <View style={styles.actionButtonContent}>
            <Winicon src="fill/shopping/cart" size={24} color="#fff" />
            <Text style={styles.actionButtonText}>Thêm vào giỏ hàng</Text>
          </View>
        </TouchableOpacity>
        <TouchableOpacity style={styles.buyButton}>
          <View style={styles.actionButtonContent}>
            <Winicon src="fill/shopping/box-ribbon" size={24} color="#fff" />
            <Text style={styles.actionButtonText}>Mua ngay</Text>
          </View>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'transparent',
    position: 'absolute',
    top: 30,
    left: 0,
    right: 0,
    zIndex: 10,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  cartButton: {
    position: 'relative',
  },
  cartBadge: {
    position: 'absolute',
    top: -5,
    right: -5,
    backgroundColor: '#FF4D4F',
    borderRadius: 10,
    width: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartBadgeText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  scrollView: {
    flex: 1,
  },
  imageContainer: {
    width: '100%',
    height: 350,
    position: 'relative',
    backgroundColor: ColorThemes.light.neutral_main_background_color,
  },
  productImage: {
    width: Dimensions.get('window').width,
    // height: '100%',
  },
  imageCounter: {
    position: 'absolute',
    bottom: 12,
    right: 12,
    backgroundColor: 'rgba(0,0,0,0.5)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  imageCounterText: {
    color: '#fff',
    fontSize: 12,
  },
  // Thumbnail gallery styles
  thumbnailContainer: {
    padding: 16,
    paddingTop: 12,
    backgroundColor: '#fff',
  },
  thumbnailList: {
    marginTop: 8,
  },
  thumbnailItem: {
    width: 61,
    height: 61,
    borderRadius: 4,
    marginRight: 10,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    overflow: 'hidden',
  },
  selectedThumbnail: {
    borderColor: '#FF4D4F',
    borderWidth: 2,
  },
  thumbnailImage: {
    width: '100%',
    height: '100%',
  },
  // Variants section
  variantsContainer: {
    padding: 16,
    paddingTop: 0,
  },
  variantsTitle: {
    fontSize: 14,
    fontWeight: '400',
    marginBottom: 12,
    color: '#000',
    lineHeight: 22,
  },
  variantsList: {
    gap: 8,
  },
  variantItem: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    overflow: 'hidden',
    marginRight: 8,
  },
  selectedVariant: {
    borderColor: ColorThemes.light.primary_main_color,
    borderWidth: 2,
  },
  variantImage: {
    width: 60,
    height: 60,
  },
  priceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 0.3,
    borderBottomColor: '#90C8FB',
    // paddingVertical: 8,
    marginTop: 12,
  },
  priceText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FF4D4F',
    lineHeight: 32,
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  favoriteButton: {},
  shareButton: {},
  productTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#000',
    paddingHorizontal: 16,
    paddingVertical: 8,
    lineHeight: 24,
    backgroundColor: '#fff',
    borderBottomWidth: 0.3,
    borderBottomColor: '#90C8FB',
  },
  detailsContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  detailsHorizontalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  detailItem: {
    flexDirection: 'column',
    alignItems: 'center',
    paddingVertical: 4,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 4,
  },
  detailLabel: {
    fontSize: 14,
    color: '#000',
    lineHeight: 22,
    fontWeight: '400',
  },
  shippingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  shippingDistance: {
    fontSize: 12,
    color: '#3FB993',
    marginLeft: 8,
  },
  shippingFree: {
    fontSize: 13,
    color: '#3FB993',
    fontWeight: '500',
    marginLeft: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  starsContainer: {
    flexDirection: 'row',
  },
  ratingText: {
    fontSize: 14,
    color: '#000',
    marginLeft: 8,
  },
  viewAllText: {
    fontSize: 14,
    color: ColorThemes.light.primary_main_color,
    marginLeft: 'auto',
  },
  sellerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  sellerAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  sellerInfo: {
    marginLeft: 12,
  },
  sellerName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#000',
  },
  sellerMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  sellerBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  sellerBadgeText: {
    color: '#8C8C8C',
    fontSize: 12,
    marginLeft: 4,
  },
  sellerContact: {
    fontSize: 12,
    color: '#666',
    marginLeft: 8,
  },
  descriptionContainer: {
    padding: 16,
  },
  bottomActions: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
    height: 64,
  },
  chatButton: {
    flex: 1,
    backgroundColor: '#1877F2',
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartAddButton: {
    flex: 1,
    backgroundColor: '#0066CC',
    justifyContent: 'center',
    alignItems: 'center',
  },
  buyButton: {
    flex: 1,
    backgroundColor: '#FF4D4F',
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionButtonContent: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 4,
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
});
