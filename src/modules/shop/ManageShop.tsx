/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useRef, useState} from 'react';
import {StyleSheet, View} from 'react-native';
import {FDialog, FLoading} from 'wini-mobile-components';
import {useNavigation, useRoute} from '@react-navigation/native';
import {ScrollView} from 'react-native-gesture-handler';
import UserInfo from '../../components/shop/UserInfo';
import NotShop from '../../components/shop/NotShop';
import HaveShop from '../../components/shop/HaveShop';
import {TypeMenuShop} from '../../Config/Contanst';
import MenuShop from '../../components/shop/MenuShop';
import {useSelectorShopState} from '../../redux/hook/shopHook ';
import {useDispatch} from 'react-redux';
import {ProductActions} from '../../redux/reducers/ProductReducer';
import TreeAffiliate from './treeAffiliate';
import Profile from '../customer/profile';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import {ColorThemes} from '../../assets/skin/colors';
import {RootScreen} from '../../router/router';
import {InforHeader} from '../../Screen/Layout/headers/inforHeader';

const Shop = () => {
  const route = useRoute<any>();
  const [select, setSelect] = useState(
    route?.params?.select ? route?.params?.select : TypeMenuShop.User,
  );
  const shopInfo = useSelectorShopState().data;
  const [shop, setShop] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const dialogRef = useRef<any>(null);
  const dispatch = useDispatch<any>();
  //navi
  const navigation = useNavigation<any>();
  useEffect(() => {
    if (select == TypeMenuShop.Shop && shopInfo) {
      dispatch(ProductActions.getInforProduct(shopInfo[0]?.Id));
      setShop(shopInfo);
    }
    if (select === TypeMenuShop.Wallet) {
      navigation.push(RootScreen.MyWalletProfile);
    }
  }, [select, shopInfo]);
  return (
    <View style={styles.container}>
      <InforHeader title={select} showAction showBack={false} />
      <ScrollView
        showsVerticalScrollIndicator={false}
        style={{flex: 1, width: '100%'}}>
        <FDialog ref={dialogRef} />
        <FLoading visible={loading} />
        <UserInfo />
        <MenuShop select={select} setSelect={setSelect} />
        {select == TypeMenuShop.Shop && shop && shop.length > 0 ? (
          <HaveShop shop={shop} />
        ) : select == TypeMenuShop.Afiliate ? (
          <TreeAffiliate />
        ) : select == TypeMenuShop.User ? (
          <Profile />
        ) : (
          <NotShop />
        )}
        <View style={{height: 85}} />
      </ScrollView>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    maxHeight: 120,
  },

  navigator: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomColor: '#00FFFF',
    paddingBottom: 18,
    borderBottomWidth: 0.5,
  },

  icon: {
    width: 24,
    height: 24,
  },
});

export default Shop;
