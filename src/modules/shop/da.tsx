import {DataController} from '../../base/baseController';

export class ShopDA {
  private ShopController: DataController;

  constructor() {
    this.ShopController = new DataController('Shop');
  }
  async getShop(cusId: string) {
    const response = await this.ShopController.aggregateList({
      page: 1,
      size: 1,
      searchRaw: `@CustomerId: {${cusId}}`,
    });
    if (response?.code === 200) {
      return response;
    }
    return null;
  }
}

export default ShopDA;
